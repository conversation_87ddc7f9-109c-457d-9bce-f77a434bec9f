[supervisord]
logfile=/dev/stdout
logfile_maxbytes=0
loglevel=info
pidfile=/tmp/supervisord.pid
nodaemon=true
user=root

[unix_http_server]
file=/tmp/supervisor.sock

[program:fastapi]
command=uv run tiong --prod
directory=/app
environment=SSH_KEY_ID=/app/pipit_servers
# By default, supervisor saves your FastAPI logs to temporary files in /tmp, not to the container's stdout that <PERSON><PERSON> can see.
# By setting stdout_logfile=/dev/stdout, we tell supervisor to send FastAPI's output to supervisor's own stdout, which Docker can then capture and show you.
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
# Prevent infinte retries
autorestart=false

[program:openmeter-collector]
command=/usr/local/bin/openmeter-collector -c /etc/openmeter/config.yaml
# Ditto
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
# Prevent infinte retries
autorestart=false
