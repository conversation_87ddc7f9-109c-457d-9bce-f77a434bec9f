/// <reference types="vitest" />
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig(({ command }) => {
    const isProduction = command === 'build'

    return {
        plugins: [react()],
        envDir: path.resolve(__dirname, '..'),
        build: {
            outDir: '../src/tiong/app/static',
            emptyOutDir: true,
            rollupOptions: {
                input: {
                    index: './index.html',
                    listen: './listen.html',
                    broadcast: './broadcast.html',
                    broadcastTest: './broadcast-test.html'
                }
            },
            assetsDir: 'assets'
        },

        resolve: {
            alias: {
                '@': path.resolve(__dirname, './src')
            }
        },

        server: {
            port: 5173,
            proxy: {
                // Proxy API calls to backend
                '/api': {
                    target: 'http://localhost:8000',
                    changeOrigin: true
                },
                // Proxy room routes (any path that looks like a room ID)
                // This regex matches paths that could be room IDs (alphanumeric)
                '^/[a-zA-Z0-9]+$': {
                    target: 'http://localhost:8000',
                    changeOrigin: true
                }
            }
        },

        // Only set base path for production builds
        base: isProduction ? '/static/' : '/',

        // TypeScript support
        esbuild: {
            target: 'es2020'
        },

        // Test configuration
        test: {
            globals: true,
            environment: 'jsdom',
            setupFiles: ['./src/test/setup.ts']
        }
    }
})
