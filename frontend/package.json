{"name": "tiong-frontend", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build:local": "shdotenv -e ../config/local/dot-env-local vite build", "build:staging": "shdotenv -e ../config/staging/dot-env-staging vite build", "build:watch": "vite build --watch", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,html,css}\"", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.1.5", "@stripe/react-stripe-js": "^3.9.1", "@stripe/stripe-js": "^7.8.0", "@supabase/supabase-js": "^2.50.2", "can-autoplay": "^3.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "livekit-client": "^2.0.0", "loglevel": "^1.9.2", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.8.2", "react-toastify": "^11.0.5", "tailwind-merge": "^1.14.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/can-autoplay": "^3.0.5", "@types/jest": "^30.0.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.1.0", "@wdio/browser-runner": "^9.18.1", "@wdio/cli": "^9.18.1", "@wdio/mocha-framework": "^9.18.0", "@wdio/spec-reporter": "^9.18.0", "autoprefixer": "^10.4.16", "depcheck": "^1.4.7", "jsdom": "^26.1.0", "lit": "^3.3.1", "postcss": "^8.4.31", "prettier": "^3.5.3", "sinon": "^21.0.0", "tailwindcss": "^3.3.5", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "vite": "^5.0.8", "vitest": "^3.2.4"}}