<!DOCTYPE html>
<html lang="en" class="scroll-smooth">

<head>
  <!-- Cookie<PERSON> <PERSON><PERSON> -->
  <script id="Cookiebot" src="https://consent.cookiebot.com/uc.js" data-cbid="a36d8d92-63e9-43ed-a611-4457446747a2"
    type="text/javascript" async></script>

  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-3ZQ6MZT0KK"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      dataLayer.push(arguments);
    }
    gtag("js", new Date());

    gtag("config", "G-3ZQ6MZT0KK");
  </script>

  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>VoiceFrom.ai - Live AI Interpretation</title>

  <!-- Favicons -->
  <link rel="icon" type="image/png" href="assets/favicon/favicon-96x96.png" sizes="96x96" />
  <link rel="icon" type="image/svg+xml" href="assets/favicon/favicon.svg" />
  <link rel="shortcut icon" href="assets/favicon/favicon.ico" />
  <link rel="apple-touch-icon" sizes="180x180" href="assets/favicon/apple-touch-icon.png" />
  <link rel="manifest" href="assets/favicon/site.webmanifest" />

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link
    href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@400;600&family=IBM+Plex+Mono:wght@400;600&display=swap"
    rel="stylesheet" />

  <!-- Tailwind (CDN) -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: {
              DEFAULT: "#0073eb",
              light: "#66a9ff",
              dark: "#0055b3",
            },
          },
          fontFamily: {
            sans: ["IBM Plex Sans", "ui-sans-serif", "system-ui"],
            mono: ["IBM Plex Mono", "ui-monospace", "SFMono-Regular"],
          },
        },
      },
    };
  </script>

  <!-- Helper classes -->
  <style>
    /* shared spacing + width */
    .section {
      padding-top: 2rem;
      padding-bottom: 3rem;
      width: 100%;
    }

    /* light & dark background helpers */
    .bg-light {
      background: #ffffff;
    }

    .dark .bg-light {
      background: #111827;
    }

    /* gray-900 */
    .bg-muted {
      background: #f9fafb;
    }

    /* gray-50 */
    .dark .bg-muted {
      background: #1f2937;
    }

    /* gray-800 */

    /* common paragraph style used across sections */
    .section-paragraph {
      text-align: left;
      color: #4b5563;
      /* gray-600 */
      font-size: 1rem;
      line-height: 1.5rem;
      /* base */
    }

    @media(min-width:640px) {
      .section-paragraph {
        font-size: 1.125rem;
        line-height: 1.75rem;
      }

      /* sm:text-lg */
    }

    .dark .section-paragraph {
      color: #9ca3af;
    }

    /* gray-400 */

    /* iframe appearance helper */
    .demo-video {
      width: 100%;
      aspect-ratio: 16/9;
      /* aspect-video */
      border-radius: 0.5rem;
      /* rounded-lg */
      box-shadow: 0 1px 2px 0 rgb(0 0 0 / .05);
      /* shadow */
    }

    /* input styling helper */
    .input-style {
      border-radius: .375rem;
      /* rounded-md */
      border: 1px solid #d1d5db;
      /* gray-300 */
      background: rgba(255, 255, 255, .90);
      /* bg-white/90 */
    }

    .dark .input-style {
      border-color: #4b5563;
      /* gray-600 */
      background: rgba(31, 41, 55, .80);
      /* bg-gray-800/80 */
    }
  </style>

  <!-- MailerLite loader -->
  <script>
    (function (w, d, e, u, f, l, n) {
      (w[f] =
        w[f] ||
        function () {
          (w[f].q = w[f].q || []).push(arguments);
        }),
        (l = d.createElement(e)),
        (l.async = 1),
        (l.src = u),
        (n = d.getElementsByTagName(e)[0]),
        n.parentNode.insertBefore(l, n);
    })(
      window,
      document,
      "script",
      "https://assets.mailerlite.com/js/universal.js",
      "ml"
    );
    ml("account", "1581870");
  </script>
</head>

<body class="min-h-screen flex flex-col bg-white text-gray-700 dark:bg-gray-900 dark:text-gray-300 font-sans">
  <!-- Navbar -->
  <header class="w-full sticky top-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur">
    <div class="max-w-6xl mx-auto flex items-center justify-between px-4 py-4">
      <a href="#home" class="flex items-center gap-2 font-normal text-gray-900 dark:text-white text-[1.8rem]">
        <img src="assets/voicefrom-logo.png" alt="VoiceFrom.ai logo" class="h-8 w-auto mt-0.5" />
        <span class="font-semibold">VoiceFrom.ai</span>
      </a>
      <nav class="space-x-6 hidden sm:block font-sans text-sm">
        <a href="#home" class="hover:text-primary">Home</a>
        <!-- <a href="#how-to-use" class="hover:text-primary">Tutorial</a> -->
        <a href="#demos" class="hover:text-primary">Demos</a>
        <a href="#contact" class="hover:text-primary">Contact</a>
        <a href="#about" class="hover:text-primary">About</a>
      </nav>
    </div>
  </header>

  <!-- Hero -->
  <section id="home" class="flex flex-col w-full items-start justify-center flex-grow px-4 py-24 max-w-6xl mx-auto">
    <h1 class="font-sans text-4xl sm:text-6xl font-extrabold text-gray-900 dark:text-white leading-tight text-left">
      Live AI Interpretation
    </h1>
    <p class="section-paragraph mt-6 max-w-prose">
      <strong>Myna</strong>, our state-of-the-art interpretation engine, helps
      you communicate and connect across languages in real time, so you can
      <strong>share</strong>, <strong>teach</strong>, <strong>help</strong>,
      or <strong>chat</strong> with <strong>anyone</strong>.
    </p>

    <button id="openWaitlist"
      class="mt-8 inline-flex items-center gap-3 rounded-full bg-primary px-6 py-3 font-mono text-white shadow-md hover:bg-primary/90 focus:outline-none focus:ring-4 focus:ring-primary/40">
      Join the Waitlist
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"
        stroke-width="2">
        <path stroke-linecap="round" stroke-linejoin="round" d="M3 10l9-9m0 0l9 9m-9-9v18" />
      </svg>
    </button>
  </section>

  <!-- How to Use -->
  <!-- <section id="how-to-use" class="section bg-white">
    <div class="max-w-6xl mx-auto px-4">
      <h2 class="font-sans text-3xl sm:text-4xl font-bold mb-10 text-gray-900 dark:text-black">
        How to Use Myna
      </h2>
      <p class="section-paragraph mb-8 max-w-3xl">
        Watch our complete walkthrough to see how easy it is to get started with live AI interpretation.
      </p>

      <div class="relative max-w-4xl mx-auto mb-12">
        <div class="relative overflow-hidden rounded-2xl shadow-2xl bg-gradient-to-br from-primary/10 to-primary/5 p-1">
          <div class="relative overflow-hidden rounded-xl bg-black">
            <iframe class="w-full aspect-video"
              src="https://www.youtube.com/embed/3xZr8iANOi8?rel=0&modestbranding=1&showinfo=0"
              title="How To Use Myna - Complete Walkthrough"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowfullscreen></iframe>
          </div>
        </div>
      </div>
    </div>
  </section> -->

  <!-- Demos -->
  <section id="demos" class="section bg-muted">
    <div class="max-w-6xl mx-auto px-4">
      <h2 class="font-sans text-3xl sm:text-4xl font-bold mb-10 text-gray-900 dark:text-black">
        Demos
      </h2>
      <div class="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">

        <figure class="flex flex-col gap-4">
          <iframe class="demo-video" src="https://www.youtube.com/embed/L0X96ZR4-Bc" title="Sample Demo" loading="lazy"
            allowfullscreen></iframe>
          <figcaption class="text-center text-sm font-mono space-y-1">
            <strong>Arabic → English (experimental)</strong>
            <span class="block text-gray-500">WHA · speech</span>
            <a href="https://youtu.be/BL6Lw8pL_to" target="_blank" rel="noopener"
              class="block text-primary text-xs hover:underline">original</a>
          </figcaption>
        </figure>

        <figure class="flex flex-col gap-4">
          <iframe class="demo-video" src="https://www.youtube.com/embed/QH92AAq8m_Y" title="Sample Demo" loading="lazy"
            allowfullscreen></iframe>
          <figcaption class="text-center text-sm font-mono space-y-1">
            <strong>Arabic → French (experimental)</strong>
            <span class="block text-gray-500">WHA · speech</span>
            <a href="https://youtu.be/BL6Lw8pL_to" target="_blank" rel="noopener"
              class="block text-primary text-xs hover:underline">original</a>
          </figcaption>
        </figure>

        <figure class="flex flex-col gap-4">
          <iframe class="demo-video" src="https://www.youtube.com/embed/oXHudYyynyU" title="Sample Demo" loading="lazy"
            allowfullscreen></iframe>
          <figcaption class="text-center text-sm font-mono space-y-1">
            <strong>Spanish → German</strong>
            <span class="block text-gray-500">robotics · startup pitch</span>
            <a href="https://youtu.be/jWX31wZUTN0" target="_blank" rel="noopener"
              class="block text-primary text-xs hover:underline">original</a>
          </figcaption>
        </figure>

        <figure class="flex flex-col gap-4">
          <iframe class="demo-video" src="https://www.youtube.com/embed/Rd2zKwf4iJY" title="Sample Demo" loading="lazy"
            allowfullscreen></iframe>
          <figcaption class="text-center text-sm font-mono space-y-1">
            <strong>Spanish → English</strong>
            <span class="block text-gray-500">healthcare · patient interview</span>
            <a href="https://youtu.be/b2qZOqUGe48" target="_blank" rel="noopener"
              class="block text-primary text-xs hover:underline">original</a>
          </figcaption>
        </figure>

        <figure class="flex flex-col gap-4">
          <iframe class="demo-video" src="https://www.youtube.com/embed/16TH3cs4LTc" title="Sample Demo" loading="lazy"
            allowfullscreen></iframe>
          <figcaption class="text-center text-sm font-mono space-y-1">
            <strong>English → German</strong>
            <span class="block text-gray-500">venture capital · monologue</span>
            <a href="https://youtu.be/Drse9NLKG2s" target="_blank" rel="noopener"
              class="block text-primary text-xs hover:underline">original</a>
          </figcaption>
        </figure>

        <figure class="flex flex-col gap-4">
          <iframe class="demo-video" src="https://www.youtube.com/embed/Yr5-9b6stF4" title="Sample Demo" loading="lazy"
            allowfullscreen></iframe>
          <figcaption class="text-center text-sm font-mono space-y-1">
            <strong>Turkish → English (experimental)</strong>
            <span class="block text-gray-500">inspirational · speech</span>
            <a href="https://youtu.be/V_CjV7JacCM" target="_blank" rel="noopener"
              class="block text-primary text-xs hover:underline">original</a>
          </figcaption>
        </figure>

        <figure class="flex flex-col gap-4">
          <iframe class="demo-video" src="https://www.youtube.com/embed/fgyJgdwua7s" title="Sample Demo" loading="lazy"
            allowfullscreen></iframe>
          <figcaption class="text-center text-sm font-mono space-y-1">
            <strong>Polish → English (experimental)</strong>
            <span class="block text-gray-500">e-commerce · monologue</span>
            <a href="https://youtu.be/Q8nUIgYefeQ" target="_blank" rel="noopener"
              class="block text-primary text-xs hover:underline">original</a>
          </figcaption>
        </figure>

        <!-- <figure class="flex flex-col gap-4">
          <iframe class="demo-video" src="https://www.youtube.com/embed/jOQzKf_sLNc" title="Sample Demo" loading="lazy"
            allowfullscreen></iframe>
          <figcaption class="text-center text-sm font-mono space-y-1">
            <strong>German → Italian (experimental)</strong>
            <span class="block text-gray-500">aircraft · documentary</span>
            <a href="https://youtu.be/rlEfw77xlOo" target="_blank" rel="noopener"
              class="block text-primary text-xs hover:underline">original</a>
          </figcaption>
        </figure>

        <figure class="flex flex-col gap-4">
          <iframe class="demo-video" src="https://www.youtube.com/embed/GZnFjzSJ2do" title="Sample Demo" loading="lazy"
            allowfullscreen></iframe>
          <figcaption class="text-center text-sm font-mono space-y-1">
            <strong>Portuguese → English (experimental)</strong>
            <span class="block text-gray-500">song analysis · commentary</span>
            <a href="https://youtu.be/gq32pvNgXm8" target="_blank" rel="noopener"
              class="block text-primary text-xs hover:underline">original</a>
          </figcaption>
        </figure>

        <figure class="flex flex-col gap-4">
          <iframe class="demo-video" src="https://www.youtube.com/embed/Dbj6uHq637M" title="Sample Demo" loading="lazy"
            allowfullscreen></iframe>
          <figcaption class="text-center text-sm font-mono space-y-1">
            <strong>German → English</strong>
            <span class="block text-gray-500">hospital admission · healthcare</span>
            <a href="https://youtu.be/HwWwNWB7JMs" target="_blank" rel="noopener"
              class="block text-primary text-xs hover:underline">original</a>
          </figcaption>
        </figure>

        <figure class="flex flex-col gap-4">
          <iframe class="demo-video" src="https://www.youtube.com/embed/RZVlNabpdXI" title="Sample Demo" loading="lazy"
            allowfullscreen></iframe>
          <figcaption class="text-center text-sm font-mono space-y-1">
            <strong>German → Arabic (experimental)</strong>
            <span class="block text-gray-500">patient interview · healthcare</span>
            <a href="https://youtu.be/OgEWyL4FUds" target="_blank" rel="noopener"
              class="block text-primary text-xs hover:underline">original</a>
          </figcaption>
        </figure>

        <figure class="flex flex-col gap-4">
          <iframe class="demo-video" src="https://www.youtube.com/embed/J4jZgamEzfg" title="Sample Demo" loading="lazy"
            allowfullscreen></iframe>
          <figcaption class="text-center text-sm font-mono space-y-1">
            <strong>French → English</strong>
            <span class="block text-gray-500">ai · interview</span>
            <a href="https://youtu.be/Zw3_bj11mZY" target="_blank" rel="noopener"
              class="block text-primary text-xs hover:underline">original</a>
          </figcaption>
        </figure>

        <figure class="flex flex-col gap-4">
          <iframe class="demo-video" src="https://www.youtube.com/embed/6F1kTNFtPgI" title="Sample Demo" loading="lazy"
            allowfullscreen></iframe>
          <figcaption class="text-center text-sm font-mono space-y-1">
            <strong>Italian → English (experimental)</strong>
            <span class="block text-gray-500">design · interview</span>
            <a href="https://youtu.be/QjMRENNHBlI" target="_blank" rel="noopener"
              class="block text-primary text-xs hover:underline">original</a>
          </figcaption>
        </figure>

        <figure class="flex flex-col gap-4">
          <iframe class="demo-video" src="https://www.youtube.com/embed/OftEPK5nDU8" title="Sample Demo" loading="lazy"
            allowfullscreen></iframe>
          <figcaption class="text-center text-sm font-mono space-y-1">
            <strong>Spanish → English</strong>
            <span class="block text-gray-500">poetry</span>
            <a href="https://www.youtube.com/watch?v=GLaGvaUKB6s&list=PLaTfzV36cBRcC5lfCbUwic9W2GxZVbYg-&index=1"
              target="_blank" rel="noopener" class="block text-primary text-xs hover:underline">original</a>
          </figcaption>
        </figure>

        <figure class="flex flex-col gap-4">
          <iframe class="demo-video" src="https://www.youtube.com/embed/z0SJr0__mW0" title="Sample Demo" loading="lazy"
            allowfullscreen></iframe>
          <figcaption class="text-center text-sm font-mono space-y-1">
            <strong>Portuguese → English (experimental)</strong>
            <span class="block text-gray-500">football · sport commentary</span>
            <a href="https://youtu.be/z0SJr0__mW0" target="_blank" rel="noopener"
              class="block text-primary text-xs hover:underline">original</a>
          </figcaption>
        </figure>

        <figure class="flex flex-col gap-4">
          <iframe class="demo-video" src="https://www.youtube.com/embed/EIa5K5BFYWs" title="Sample Demo" loading="lazy"
            allowfullscreen></iframe>
          <figcaption class="text-center text-sm font-mono space-y-1">
            <strong>German → Spanish (experimental)</strong>
            <span class="block text-gray-500">poem · recital</span>
            <a href="https://youtu.be/iWbyvUu26QM" target="_blank" rel="noopener"
              class="block text-primary text-xs hover:underline">original</a>
          </figcaption>
        </figure> -->

      </div>
    </div>
  </section>

  <!-- Contact -->
  <section id="contact" class="section bg-light">
    <div class="max-w-6xl mx-auto px-4 flex flex-col items-start justify-center">
      <h2 class="font-sans text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white leading-tight text-left">
        Get in Touch
      </h2>

      <p class="section-paragraph mt-6 max-w-prose">
        Curious about Myna, exploring a partnership, or have other questions?
        Just reach out at
        <a href="mailto:<EMAIL>" class="text-primary hover:underline">
          <EMAIL>
        </a>,
        and we'll get back to you within one business day.
      </p>
    </div>
  </section>

  <!-- About -->
  <section id="about" class="section bg-muted">
    <div class="max-w-6xl mx-auto px-4">
      <h2 class="font-sans text-3xl sm:text-4xl font-bold mb-6 text-gray-900 dark:text-black">
        About Us
      </h2>
      <p class="section-paragraph max-w-prose">
        VoiceFrom.ai is an audio-focused artificial intelligence company
        founded by Dominik&nbsp;Roblek and Hassan&nbsp;Rom, both former Google
        Audio&nbsp;AI researchers.
      </p>
    </div>
  </section>

  <!-- Footer -->
  <footer class="mt-auto bg-gray-100 dark:bg-gray-800 py-8">
    <div
      class="max-w-6xl mx-auto px-4 flex flex-col sm:flex-row items-center justify-between gap-4 text-sm text-gray-500 dark:text-gray-400">
      <p>VoiceFrom Inc., 160 Battery Street East, Suite 100, San Francisco, CA 94111</p>
      <div class="flex space-x-4">
        <a href="https://x.com/VoiceFromAI" class="hover:text-primary">X</a>
        <a href="https://github.com/VoiceFrom" class="hover:text-primary">GitHub</a>
      </div>
    </div>
    <p class="mt-4 text-center text-sm text-gray-400 dark:text-gray-500">
      Translations are provided as-is; VoiceFrom Inc. accepts no responsibility
      for any outcomes.
    </p>
  </footer>

  <!-- current year -->
  <script>
    document.getElementById("year").textContent = new Date().getFullYear();
  </script>

  <!-- Waitlist modal -->
  <dialog id="waitlistModal" class="backdrop:bg-black/40 rounded-xl p-0">
    <div class="w-full max-w-md bg-white dark:bg-gray-900 rounded-xl px-6 py-8 font-sans">
      <!-- close button -->
      <button type="button" onclick="this.closest('dialog').close()"
        class="float-right text-2xl leading-none font-bold text-gray-400 hover:text-gray-600 dark:text-gray-500">
        &times;
      </button>

      <!-- heading & sub-heading -->
      <div id="formHeading">
        <h2 class="text-2xl font-semibold mb-2">Join the Waitlist</h2>
        <p class="text-base text-gray-600 dark:text-gray-400 mb-4">
          Sign up to get early access to VoiceFrom.ai.
        </p>
      </div>

      <!-- success view (hidden until callback fires) -->
      <div id="successMessage" class="hidden text-center space-y-4">
        <h2 class="text-2xl font-semibold">Thank you!</h2>
        <p class="text-base text-gray-600 dark:text-gray-300">
          You've successfully joined VoiceFrom.ai waitlist 🎉<br />
          We'll notify you by email as soon as early access becomes available.
        </p>
        <p class="text-base text-gray-600 dark:text-gray-300">
          <a href="https://x.com/VoiceFromAI" class="text-primary hover:underline" target="_blank">
            Follow us on X
          </a>
          to stay updated in the meantime.
        </p>
      </div>

      <!-- MailerLite form -->
      <form id="formContent" class="ml-block-form space-y-4"
        action="https://assets.mailerlite.com/jsonp/1581870/forms/156707208750433641/subscribe" data-code="">
        <input type="email" name="fields[email]" required placeholder="<EMAIL>" autocomplete="email"
          class="input-style w-full px-4 py-3 focus:ring-2 focus:ring-primary outline-none" />

        <!-- MailerLite hidden fields -->
        <input type="hidden" name="ml-submit" value="1" />
        <input type="hidden" name="anticsrf" value="true" />

        <button type="submit"
          class="w-full rounded-full bg-primary px-6 py-3 font-mono text-white shadow-md hover:bg-primary/90 focus:outline-none focus:ring-4 focus:ring-primary/40">
          Subscribe
        </button>
      </form>
    </div>
  </dialog>

  <!-- open modal & success callback -->
  <script>
    document.getElementById("openWaitlist").addEventListener("click", (e) => {
      e.preventDefault();
      // Reset form state when opening the modal
      document.getElementById("formContent").classList.remove("hidden");
      document.getElementById("formHeading").classList.remove("hidden");
      document.getElementById("successMessage").classList.add("hidden");
      document.getElementById("formContent").reset();
      document.getElementById("waitlistModal").showModal();
    });

    /* called by MailerLite JSONP on successful subscribe */
    function ml_callback_156707208750433641(response) {
      if (response.success) {
        document.getElementById("formContent").classList.add("hidden");
        document.getElementById("formHeading").classList.add("hidden");
        document.getElementById("successMessage").classList.remove("hidden");
      }
    }

    // Handle form submission
    document
      .getElementById("formContent")
      .addEventListener("submit", function (e) {
        e.preventDefault();

        // Get the form data
        const email = this.querySelector('input[name="fields[email]"]').value;

        // Create the submission URL with a callback
        const formAction = this.getAttribute("action");
        const callbackName = "ml_callback_156707208750433641";

        // Create and append script for JSONP request
        const script = document.createElement("script");
        script.src = `${formAction}?fields[email]=${encodeURIComponent(
          email
        )}&callback=${callbackName}`;
        document.body.appendChild(script);

        // Remove the script after execution
        script.onload = function () {
          document.body.removeChild(script);
        };
      });
  </script>
  <!-- MailerLite form helper (handles AJAX + success callback) -->
  <script src="https://groot.mailerlite.com/js/w/webforms.min.js"></script>
</body>

</html>
