/**
 * Entry point for the Broadcaster React application
 */

import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'

import { ToastContainer } from 'react-toastify'
import ErrorBoundary from './components/ErrorBoundary'
import './globals.css'
import { BroadcastApp } from './components/BroadcastApp'
import { RouterProvider, createBrowserRouter } from 'react-router-dom'

// Data router configuration
const router = createBrowserRouter([
    {
        path: '/*',
        element: <BroadcastApp />
    }
])

// Mount the React app
const rootElement = document.getElementById('root')

if (rootElement) {
    const root = ReactDOM.createRoot(rootElement)
    root.render(
        <React.StrictMode>
            <ErrorBoundary>
                <RouterProvider router={router} />
                <ToastContainer
                    position="bottom-right"
                    autoClose={5000}
                    hideProgressBar={true}
                    newestOnTop={true}
                    closeOnClick={true}
                />
            </ErrorBoundary>
        </React.StrictMode>
    )
} else {
    console.error('Failed to find root element for React app')
}
