import { TopUpCard, PaymentHistoryCard } from '@/components/billing'
import { AuthenticatedFetch } from '@/hooks/useAuth'

interface BillingComponentProps {
    authenticatedFetch: AuthenticatedFetch
}

export function BillingComponent({
    authenticatedFetch
}: BillingComponentProps) {
    return (
        <div className="space-y-8">
            <TopUpCard authenticatedFetch={authenticatedFetch} />
            <PaymentHistoryCard authenticatedFetch={authenticatedFetch} />
        </div>
    )
}
