/**
 * Main Broadcast Application - Handles authentication and dashboard for broadcasters
 */

import { Dashboard } from './dashboard/Dashboard'
import { useAuth } from '@/hooks/useAuth'
import { AuthForm } from './AuthForm'
import { BroadcastLayout } from './BroadcastLayout'
import { ControllerComponent } from './ControllerComponent'
import { SessionComponent } from './SessionComponent'
import { BillingComponent } from './BillingComponent'
import { Routes, Route, Navigate, useNavigate } from 'react-router-dom'
import { useState } from 'react'
import type { SessionDetails } from '@/hooks/useSessionManager'

export function BroadcastApp() {
    const [headerTitle, setHeaderTitle] = useState<string>('')
    const {
        authState,
        signIn,
        signInWithGoogle,
        signUp,
        user,
        loading,
        authenticatedFetch
    } = useAuth()

    const navigate = useNavigate()

    const openBroadcastControl = (session: SessionDetails) => {
        navigate(
            `/room/${session.url_identifier}/session/${session.session_id}/controller`,
            {
                state: {
                    session
                }
            }
        )
    }

    const backToDashboard = () => {
        navigate('/broadcast')
    }

    // If not authenticated, show auth form
    if (authState !== 'authenticated') {
        return (
            <AuthForm
                signIn={signIn}
                signInWithGoogle={() => signInWithGoogle('/broadcast')}
                signUp={(email, password, confirmPassword) =>
                    signUp(email, password, confirmPassword, '/broadcast')
                }
                loading={loading}
            />
        )
    }

    return (
        <Routes>
            <Route
                path="/broadcast"
                element={
                    <BroadcastLayout>
                        <Dashboard
                            authenticatedFetch={authenticatedFetch}
                            userId={user!.id}
                            onNavigateToBroadcast={openBroadcastControl}
                        />
                    </BroadcastLayout>
                }
            />
            <Route
                path="room/:urlIdentifier/session/:sessionId"
                element={
                    <BroadcastLayout
                        title={headerTitle}
                        onTitleClick={backToDashboard}
                    >
                        <SessionComponent
                            authenticatedFetch={authenticatedFetch}
                            userId={user!.id}
                            onBackToDashboard={backToDashboard}
                            onTitleChange={setHeaderTitle}
                        />
                    </BroadcastLayout>
                }
            >
                {/* If someone opens /room/:urlIdentifier/session/:sessionId directly, move to /room/:urlIdentifier/session/:sessionId/controller */}
                <Route index element={<Navigate to="controller" replace />} />
                <Route path="controller" element={<ControllerComponent />} />
            </Route>
            <Route
                path="/billing"
                element={
                    <BroadcastLayout
                        title="Billing"
                        onTitleClick={backToDashboard}
                    >
                        <BillingComponent
                            authenticatedFetch={authenticatedFetch}
                        />
                    </BroadcastLayout>
                }
            />
            <Route path="*" element={<Navigate to="/broadcast" replace />} />
        </Routes>
    )
}
