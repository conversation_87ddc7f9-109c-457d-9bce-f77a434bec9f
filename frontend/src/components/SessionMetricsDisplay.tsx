/**
 * SessionMetricsDisplay - Component for displaying real-time session metrics
 */

import { useSessionMetrics } from '@/hooks/useSessionMetrics'
import { AuthenticatedFetch } from '@/hooks/useAuth'
import { Card, CardContent } from '@/components/ui/Card'

interface SessionMetricsDisplayProps {
    authenticatedFetch: AuthenticatedFetch
    sessionId: string
}

export function SessionMetricsDisplay({
    authenticatedFetch,
    sessionId
}: SessionMetricsDisplayProps) {
    const { sessionMetrics, loading, error } = useSessionMetrics(
        authenticatedFetch,
        sessionId
    )

    // Helper function to format seconds to readable time
    const formatTime = (seconds: number): string => {
        if (seconds < 60) {
            return `${seconds.toFixed(1)}s`
        } else if (seconds < 3600) {
            const minutes = Math.floor(seconds / 60)
            const remainingSeconds = Math.floor(seconds % 60)
            return `${minutes}m ${remainingSeconds}s`
        } else {
            const hours = Math.floor(seconds / 3600)
            const remainingMinutes = Math.floor((seconds % 3600) / 60)
            return `${hours}h ${remainingMinutes}m`
        }
    }

    // Helper function to format cost
    const formatCost = (cost: number): string => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 4,
            maximumFractionDigits: 4
        }).format(cost)
    }

    return (
        <Card>
            <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                    <h2 className="text-xl font-semibold">Session Metrics</h2>
                    {loading && (
                        <div className="flex items-center text-sm text-gray-500">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"></div>
                            Updating...
                        </div>
                    )}
                </div>

                {error ? (
                    <div className="bg-red-50 border border-red-200 rounded-md p-4">
                        <div className="flex">
                            <div className="flex-shrink-0">
                                <svg
                                    className="h-5 w-5 text-red-400"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                >
                                    <path
                                        fillRule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-red-800">
                                    Error loading metrics
                                </h3>
                                <div className="mt-2 text-sm text-red-700">
                                    {error}
                                </div>
                                <button
                                    onClick={() => window.location.reload()}
                                    className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
                                >
                                    Retry
                                </button>
                            </div>
                        </div>
                    </div>
                ) : sessionMetrics ? (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="bg-blue-50 rounded-lg p-4">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <svg
                                        className="h-8 w-8 text-blue-600"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                        />
                                    </svg>
                                </div>
                                <div className="ml-4">
                                    <dt className="text-sm font-medium text-blue-900">
                                        AI Interpreter Time
                                    </dt>
                                    <dd className="text-2xl font-bold text-blue-600">
                                        {formatTime(
                                            sessionMetrics
                                                .ai_interpreter_seconds.total
                                        )}
                                    </dd>
                                </div>
                            </div>
                        </div>

                        <div className="bg-green-50 rounded-lg p-4">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <svg
                                        className="h-8 w-8 text-green-600"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                                        />
                                    </svg>
                                </div>
                                <div className="ml-4">
                                    <dt className="text-sm font-medium text-green-900">
                                        Total Listener Time
                                    </dt>
                                    <dd className="text-2xl font-bold text-green-600">
                                        {formatTime(
                                            sessionMetrics
                                                .total_listeners_seconds.total
                                        )}
                                    </dd>
                                </div>
                            </div>
                        </div>

                        <div className="bg-purple-50 rounded-lg p-4">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <svg
                                        className="h-8 w-8 text-purple-600"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                        />
                                    </svg>
                                </div>
                                <div className="ml-4">
                                    <dt className="text-sm font-medium text-purple-900">
                                        Total Cost
                                    </dt>
                                    <dd className="text-2xl font-bold text-purple-600">
                                        {formatCost(
                                            sessionMetrics.total_cost.total
                                        )}
                                    </dd>
                                </div>
                            </div>
                        </div>
                    </div>
                ) : (
                    <div className="text-center py-8 text-gray-500">
                        <svg
                            className="mx-auto h-12 w-12 text-gray-400"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                            />
                        </svg>
                        <p className="mt-2">No metrics data available</p>
                        <p className="text-sm text-gray-400">
                            Metrics will appear once the session starts
                        </p>
                    </div>
                )}

                {sessionMetrics && (
                    <div className="mt-6 pt-4 border-t border-gray-200">
                        <p className="text-xs text-gray-500 text-center">
                            Metrics update every 30 seconds • Session ID:{' '}
                            {sessionId}
                        </p>
                    </div>
                )}
            </CardContent>
        </Card>
    )
}
