import { useEffect } from 'react'
import { useToast } from '@/hooks/useToast'
import { Button } from '@/components/ui/Button'

interface AudioPermissionToastProps {
    show: boolean
    onClick?: () => void
}

export function AudioPermissionToast({
    show,
    onClick
}: AudioPermissionToastProps) {
    const { toast, dismiss } = useToast()

    useEffect(() => {
        if (show && onClick) {
            const toastId = toast({
                title: '🎧 Audio Permission Required',
                description:
                    'Click to enable audio playback for the best listening experience.',
                action: (
                    <Button
                        variant="default"
                        size="sm"
                        onClick={() => {
                            onClick()
                            dismiss()
                        }}
                        className="ml-auto"
                    >
                        Enable Audio
                    </Button>
                )
            })

            return () => {
                toastId.dismiss()
            }
        }
    }, [show, onClick])

    return null // The toast is rendered by the Toaster component
}
