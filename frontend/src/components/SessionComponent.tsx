import { Outlet, useLocation, useParams } from 'react-router-dom'
import { useEffect, useState, useCallback } from 'react'
import { useSessionManager, SessionDetails } from '@/hooks/useSessionManager'
import { AuthenticatedFetch } from '@/hooks/useAuth'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import { Skeleton } from '@/components/ui/Skeleton'
import { Button } from '@/components/ui/Button'
import { ArrowLeft } from 'lucide-react'
import { logger } from '@/logger'
import { toast } from 'react-toastify'

export type SessionContext = {
    session: SessionDetails
    leaveAndEndSession: () => void
}

interface SessionComponentProps {
    authenticatedFetch: AuthenticatedFetch
    userId: string
    onBackToDashboard: () => void
    onTitleChange: (title: string) => void
}

export function SessionComponent({
    authenticatedFetch,
    userId,
    onBackToDashboard,
    onTitleChange
}: SessionComponentProps) {
    const { sessionId } = useParams<{
        sessionId: string
    }>()
    const location = useLocation()

    const { lookupSession, joinSession, leaveSession, endSession, loading } =
        useSessionManager(authenticatedFetch, userId)
    const [session, setSession] = useState<SessionDetails | null>(null)
    const [error, setError] = useState<string | null>(null)

    const lookupAndJoinSession = useCallback(
        async (sessionId: string) => {
            try {
                const sessionDetails = await lookupSession(sessionId)
                const joinedSession = await joinSession(sessionDetails)

                setSession(joinedSession)
            } catch (e: any) {
                logger.error('Failed to fetch and join session:', e)
                setError(e?.message ?? 'Failed to load session')
            }
        },
        [lookupSession, joinSession]
    )

    const handleJoinSession = useCallback(
        async (session: SessionDetails) => {
            try {
                const joinedSession = await joinSession(session)

                setSession(joinedSession)
            } catch (e: any) {
                logger.error('Failed to join session:', e)
                setError(e?.message ?? 'Failed to join session')
            }
        },
        [joinSession]
    )

    useEffect(() => {
        if (!sessionId) {
            setError(
                'No session ID provided in URL. Please navigate from the dashboard.'
            )
            return
        }

        const { session: preloadedSession } = location.state || {}

        // Fast path: if we have preloaded session, use it
        if (
            preloadedSession &&
            preloadedSession.livekit_url &&
            preloadedSession.livekit_token
        ) {
            handleJoinSession(preloadedSession)
            return
        }

        // Fallback: fetch by sessionId for deep links / reloads
        lookupAndJoinSession(sessionId)
    }, [sessionId, lookupAndJoinSession, handleJoinSession])

    // Update the layout title when session data is available
    useEffect(() => {
        if (session) {
            const title = session.session_title ?? session.room_title!
            onTitleChange(title)
        }
    }, [session, onTitleChange])

    if (loading) {
        return (
            <div className="space-y-6">
                <Card className="bg-gradient-card border-0 shadow-card-soft backdrop-blur-sm">
                    <CardHeader>
                        <Skeleton className="h-8 w-3/4" />
                        <Skeleton className="h-4 w-1/2" />
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <Skeleton className="h-20 w-full" />
                    </CardContent>
                </Card>
            </div>
        )
    }

    if (error || !session) {
        return (
            <div className="flex items-center justify-center p-4">
                <Card className="bg-gradient-card border-0 shadow-card-soft backdrop-blur-sm p-6 max-w-md w-full">
                    <div className="text-center space-y-4">
                        <h2 className="text-xl font-semibold">Error</h2>
                        <p className="text-muted-foreground">
                            {error || 'Session not found or failed to load.'}
                        </p>
                        <Button variant="outline" onClick={onBackToDashboard}>
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Back to Dashboard
                        </Button>
                    </div>
                </Card>
            </div>
        )
    }

    const leaveAndEndSession = async () => {
        try {
            await leaveSession(session)
            await endSession(session)

            toast.success('The session has successfully completed')

            onBackToDashboard()
        } catch (error) {
            logger.error('Error leaving session:', error)
            toast.error('Failed to leave session')
        }
    }

    return (
        <Outlet
            context={{
                session,
                leaveAndEndSession
            }}
        />
    )
}
