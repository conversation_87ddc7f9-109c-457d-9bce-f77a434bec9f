import { Component, ErrorInfo, ReactNode } from 'react'
import { logger } from '@/logger'

interface Props {
    children: ReactNode
}

interface State {
    hasError: boolean
}

class ErrorBoundary extends Component<Props, State> {
    public override state: State = {
        hasError: false
    }

    public static getDerivedStateFromError(_: Error): State {
        // Update state so the next render will show the fallback UI.
        return { hasError: true }
    }

    public override componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        logger.error('Uncaught error:', error, errorInfo)
    }

    public override render() {
        if (this.state.hasError) {
            return (
                <div className="min-h-screen flex justify-center items-center p-5 bg-gradient-to-br from-red-500 to-orange-600 font-sans">
                    <div className="w-full max-w-lg p-8 bg-white rounded-2xl shadow-2xl text-center">
                        <h1 className="text-3xl font-bold text-gray-800 mb-2">
                            Oops! Something went wrong.
                        </h1>
                        <p className="text-gray-600 text-base mb-4">
                            We've encountered an unexpected error. Please try
                            refreshing the page.
                        </p>
                        <button
                            onClick={() => window.location.reload()}
                            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                        >
                            Refresh
                        </button>
                    </div>
                </div>
            )
        }

        return this.props.children
    }
}

export default ErrorBoundary
