/**
 * LiveKitEventsDebugPanel - A debug component that traces all LiveKit room events
 */

import { useState, useEffect, useRef } from 'react'
import { Room, RoomEvent } from 'livekit-client'
import { Card, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { logger } from '@/logger'

interface EventLog {
    id: string
    timestamp: Date
    event: string
    data?: any
    details?: string
}

interface LiveKitEventsDebugPanelProps {
    room: Room | null
    title?: string
    maxEvents?: number
    collapsed?: boolean
}

export function LiveKitEventsDebugPanel({
    room,
    title = 'LiveKit Events Debug',
    maxEvents = 100,
    collapsed = false
}: LiveKitEventsDebugPanelProps) {
    const [events, setEvents] = useState<EventLog[]>([])
    const [isCollapsed, setIsCollapsed] = useState(collapsed)
    const [autoScroll, setAutoScroll] = useState(true)
    const logContainerRef = useRef<HTMLDivElement>(null)
    const eventIdCounter = useRef(0)

    // Helper function to create event log entry
    const createEventLog = (
        event: string,
        data?: any,
        details?: string
    ): EventLog => {
        return {
            id: `event-${++eventIdCounter.current}`,
            timestamp: new Date(),
            event,
            data,
            details
        }
    }

    // Helper function to add event to log
    const addEvent = (event: string, data?: any, details?: string) => {
        const eventLog = createEventLog(event, data, details)
        setEvents((prev) => {
            const newEvents = [eventLog, ...prev]
            return newEvents.slice(0, maxEvents)
        })
    }

    // Auto-scroll to top when new events arrive
    useEffect(() => {
        if (autoScroll && logContainerRef.current) {
            logContainerRef.current.scrollTop = 0
        }
    }, [events, autoScroll])

    // Set up event listeners when room changes
    useEffect(() => {
        if (!room) {
            addEvent(
                'ROOM_DISCONNECTED',
                null,
                'Room reference removed or null'
            )
            return
        }

        // Add initial connection event
        addEvent(
            'ROOM_REFERENCE_SET',
            {
                roomName: room.name,
                state: room.state
            },
            `Room reference set: ${room.name} (state: ${room.state})`
        )

        logger.debug(
            '🐛 DEBUG: Setting up event listeners for room:',
            room.name,
            'state:',
            room.state
        )

        // If room is already connected, fire a synthetic connected event
        if (room.state === 'connected') {
            logger.log(
                '🐛 DEBUG: Room already connected, adding synthetic Connected event'
            )
            addEvent(
                'Connected',
                {
                    roomName: room.name,
                    localParticipant: room.localParticipant?.identity,
                    synthetic: true
                },
                `Already connected to room: ${room.name}`
            )
        }

        // Define all the event handlers
        const handlers = {
            [RoomEvent.Connected]: () => {
                logger.log('🐛 DEBUG: Connected event fired!')
                addEvent(
                    'Connected',
                    {
                        roomName: room.name,
                        localParticipant: room.localParticipant?.identity
                    },
                    `Successfully connected to room: ${room.name}`
                )
            },

            [RoomEvent.Disconnected]: (reason?: string) => {
                logger.log('🐛 DEBUG: Disconnected event fired!', reason)
                addEvent(
                    'Disconnected',
                    { reason },
                    `Disconnected: ${reason || 'Unknown reason'}`
                )
            },

            [RoomEvent.Reconnecting]: () => {
                addEvent('Reconnecting', null, 'Attempting to reconnect...')
            },

            [RoomEvent.Reconnected]: () => {
                addEvent('Reconnected', null, 'Successfully reconnected')
            },

            [RoomEvent.ParticipantConnected]: (participant: any) => {
                addEvent(
                    'ParticipantConnected',
                    {
                        identity: participant.identity,
                        sid: participant.sid,
                        name: participant.name
                    },
                    `Participant joined: ${participant.identity}`
                )
            },

            [RoomEvent.ParticipantDisconnected]: (participant: any) => {
                addEvent(
                    'ParticipantDisconnected',
                    {
                        identity: participant.identity,
                        sid: participant.sid
                    },
                    `Participant left: ${participant.identity}`
                )
            },

            [RoomEvent.TrackPublished]: (
                publication: any,
                participant: any
            ) => {
                logger.log('🐛 DEBUG: TrackPublished event fired!', {
                    publication,
                    participant
                })
                addEvent(
                    'TrackPublished',
                    {
                        trackSid: publication.trackSid,
                        trackName: publication.trackName,
                        kind: publication.kind,
                        source: publication.source,
                        participant: participant.identity
                    },
                    `Track published: ${publication.trackName} (${publication.kind})`
                )
            },

            // Add the actual events that LiveKit fires
            localTrackPublished: (publication: any, participant: any) => {
                logger.log('🐛 DEBUG: localTrackPublished event fired!', {
                    publication,
                    participant
                })
                addEvent(
                    'LocalTrackPublished',
                    {
                        trackSid: publication.trackSid,
                        trackName: publication.trackName,
                        kind: publication.kind,
                        source: publication.source,
                        participant: participant.identity
                    },
                    `Local track published: ${publication.trackName} (${publication.kind})`
                )
            },

            localTrackUnpublished: (publication: any, participant: any) => {
                logger.log('🐛 DEBUG: localTrackUnpublished event fired!', {
                    publication,
                    participant
                })
                addEvent(
                    'LocalTrackUnpublished',
                    {
                        trackSid: publication.trackSid,
                        trackName: publication.trackName,
                        kind: publication.kind,
                        participant: participant.identity
                    },
                    `Local track unpublished: ${publication.trackName}`
                )
            },

            [RoomEvent.TrackUnpublished]: (
                publication: any,
                participant: any
            ) => {
                logger.log('🐛 DEBUG: TrackUnpublished event fired!', {
                    publication,
                    participant
                })
                addEvent(
                    'TrackUnpublished',
                    {
                        trackSid: publication.trackSid,
                        trackName: publication.trackName,
                        kind: publication.kind,
                        participant: participant.identity
                    },
                    `Track unpublished: ${publication.trackName}`
                )
            },

            [RoomEvent.TrackSubscribed]: (
                track: any,
                publication: any,
                participant: any
            ) => {
                addEvent(
                    'TrackSubscribed',
                    {
                        trackSid: track.sid,
                        trackName: publication.trackName,
                        kind: track.kind,
                        participant: participant.identity
                    },
                    `Subscribed to track: ${publication.trackName}`
                )
            },

            [RoomEvent.TrackUnsubscribed]: (
                track: any,
                publication: any,
                participant: any
            ) => {
                addEvent(
                    'TrackUnsubscribed',
                    {
                        trackSid: track.sid,
                        trackName: publication.trackName,
                        participant: participant.identity
                    },
                    `Unsubscribed from track: ${publication.trackName}`
                )
            },

            [RoomEvent.TrackMuted]: (publication: any, participant: any) => {
                addEvent(
                    'TrackMuted',
                    {
                        trackSid: publication.trackSid,
                        trackName: publication.trackName,
                        participant: participant.identity
                    },
                    `Track muted: ${publication.trackName}`
                )
            },

            [RoomEvent.TrackUnmuted]: (publication: any, participant: any) => {
                addEvent(
                    'TrackUnmuted',
                    {
                        trackSid: publication.trackSid,
                        trackName: publication.trackName,
                        participant: participant.identity
                    },
                    `Track unmuted: ${publication.trackName}`
                )
            },

            [RoomEvent.DataReceived]: (
                payload: any,
                participant: any,
                kind: any
            ) => {
                addEvent(
                    'DataReceived',
                    {
                        participant: participant?.identity,
                        kind,
                        payloadSize: payload?.length
                    },
                    `Data received from ${participant?.identity}`
                )
            },

            [RoomEvent.ConnectionQualityChanged]: (
                quality: any,
                participant: any
            ) => {
                addEvent(
                    'ConnectionQualityChanged',
                    {
                        quality,
                        participant: participant.identity
                    },
                    `Connection quality: ${quality} for ${participant.identity}`
                )
            },

            [RoomEvent.MediaDevicesError]: (error: any) => {
                addEvent(
                    'MediaDevicesError',
                    { error: error.message },
                    `Media device error: ${error.message}`
                )
            },

            [RoomEvent.RoomMetadataChanged]: (metadata: any) => {
                addEvent(
                    'RoomMetadataChanged',
                    { metadata },
                    'Room metadata updated'
                )
            },

            [RoomEvent.ParticipantMetadataChanged]: (
                metadata: any,
                participant: any
            ) => {
                addEvent(
                    'ParticipantMetadataChanged',
                    {
                        metadata,
                        participant: participant.identity
                    },
                    `Metadata changed for ${participant.identity}`
                )
            }
        }

        // Register all event handlers
        logger.log(
            '🐛 DEBUG: Registering event handlers:',
            Object.keys(handlers)
        )
        Object.entries(handlers).forEach(([event, handler]) => {
            logger.log('🐛 DEBUG: Registering handler for event:', event)
            room.on(event as any, handler)
        })

        // Cleanup function
        return () => {
            Object.entries(handlers).forEach(([event, handler]) => {
                room.off(event as RoomEvent, handler)
            })
        }
    }, [room, maxEvents])

    const clearEvents = () => {
        setEvents([])
        eventIdCounter.current = 0
    }

    const formatTimestamp = (timestamp: Date) => {
        return (
            timestamp.toLocaleTimeString() +
            '.' +
            timestamp.getMilliseconds().toString().padStart(3, '0')
        )
    }

    const getEventColor = (eventName: string) => {
        if (
            eventName.includes('Connected') ||
            eventName.includes('Published') ||
            eventName.includes('Subscribed')
        ) {
            return 'text-green-600'
        }
        if (
            eventName.includes('Disconnected') ||
            eventName.includes('Unpublished') ||
            eventName.includes('Unsubscribed') ||
            eventName.includes('Error')
        ) {
            return 'text-red-600'
        }
        if (eventName.includes('Muted') || eventName.includes('Quality')) {
            return 'text-yellow-600'
        }
        return 'text-blue-600'
    }

    if (!room) {
        return (
            <Card>
                <CardContent className="p-4">
                    <h3 className="text-lg font-semibold text-gray-500">
                        {title}
                    </h3>
                    <p className="text-sm text-gray-400 mt-2">
                        No room connected
                    </p>
                </CardContent>
            </Card>
        )
    }

    return (
        <Card>
            <CardContent className="p-4">
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold">{title}</h3>
                    <div className="flex items-center gap-2">
                        <label className="flex items-center gap-1 text-sm">
                            <input
                                type="checkbox"
                                checked={autoScroll}
                                onChange={(e) =>
                                    setAutoScroll(e.target.checked)
                                }
                                className="w-4 h-4"
                            />
                            Auto-scroll
                        </label>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={clearEvents}
                        >
                            Clear
                        </Button>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                                addEvent(
                                    'TEST_EVENT',
                                    { test: true },
                                    'Manual test event'
                                )
                            }
                        >
                            Test
                        </Button>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setIsCollapsed(!isCollapsed)}
                        >
                            {isCollapsed ? 'Expand' : 'Collapse'}
                        </Button>
                    </div>
                </div>

                {!isCollapsed && (
                    <div
                        ref={logContainerRef}
                        className="bg-gray-900 text-green-400 font-mono text-xs p-4 rounded-lg h-64 overflow-y-auto"
                    >
                        {events.length === 0 ? (
                            <p className="text-gray-500">
                                Waiting for events...
                            </p>
                        ) : (
                            <div className="space-y-1">
                                {events.map((event) => (
                                    <div
                                        key={event.id}
                                        className="border-b border-gray-700 pb-1"
                                    >
                                        <div className="flex items-start gap-2">
                                            <span className="text-gray-400 shrink-0">
                                                {formatTimestamp(
                                                    event.timestamp
                                                )}
                                            </span>
                                            <span
                                                className={`font-semibold shrink-0 ${getEventColor(event.event)}`}
                                            >
                                                {event.event}
                                            </span>
                                            {event.details && (
                                                <span className="text-gray-300">
                                                    {event.details}
                                                </span>
                                            )}
                                        </div>
                                        {event.data && (
                                            <div className="ml-20 text-gray-400 text-xs mt-1">
                                                {JSON.stringify(
                                                    event.data,
                                                    null,
                                                    2
                                                )}
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                )}

                <div className="mt-2 text-xs text-gray-500">
                    Room: {room.name} • State: {room.state} • Events:{' '}
                    {events.length}/{maxEvents}
                </div>
            </CardContent>
        </Card>
    )
}
