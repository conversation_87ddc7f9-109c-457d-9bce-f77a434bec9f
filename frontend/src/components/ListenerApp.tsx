import { useState, useRef, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { RoomHeader } from './RoomHeader'
import { RoomManager } from './RoomManager'
import { AudioTrackManager, AudioTrackManagerRef } from './AudioTrackManager'
import { AudioPermissionToast } from './AudioPermissionToast'
import { Toaster } from '@/components/ui/Toaster'
import { useLiveKit } from '@/hooks/useLiveKit'
import { useRoomLookup } from '@/hooks/useRoomLookup'
import { RemoteParticipant, RemoteAudioTrack } from 'livekit-client'
import { logger } from '@/logger'

export function ListenerApp() {
    const [roomDetails, setRoomDetails] = useState<{
        title: string
        description: string
    } | null>(null)
    const [livekitRoomId, setLivekitRoomId] = useState<string | null>(null)
    const [isInterfaceDisabled, setIsInterfaceDisabled] = useState(true)
    const [showAudioPermissionToast, setShowAudioPermissionToast] =
        useState(false)
    const [onClickToListen, setOnClickToListen] = useState<
        (() => void) | undefined
    >()
    const [activeLanguage, setActiveLanguage] = useState<string | null>(null)

    const audioTrackManagerRef = useRef<AudioTrackManagerRef>(null)

    const {
        getLivekitRoomId,
        getRoomDetails,
        loading: roomLookupLoading
    } = useRoomLookup()

    const { participants, connectionStatus, error, registerEventHandler } =
        useLiveKit(livekitRoomId, activeLanguage)

    useEffect(() => {
        const extractRoomDetails = async () => {
            const path = window.location.pathname
            if (path.startsWith('/listen/')) {
                const urlIdentifier = path.substring('/listen/'.length)
                const roomDetails = await getRoomDetails(urlIdentifier)
                setRoomDetails(roomDetails)
            }
        }
        extractRoomDetails()
    }, [getRoomDetails])

    useEffect(() => {
        const extractRoomIdFromUrl = async () => {
            const path = window.location.pathname
            if (path.startsWith('/listen/')) {
                const urlIdentifier = path.substring('/listen/'.length)
                const livekitRoomId = await getLivekitRoomId(urlIdentifier)
                logger.debug(`livekitRoomId ${livekitRoomId}`)
                setLivekitRoomId(livekitRoomId)
            }
        }

        extractRoomIdFromUrl()
    }, [getLivekitRoomId])

    // Register event handlers for LiveKit events
    useEffect(() => {
        const unsubscribers: (() => void)[] = []

        // Handle track subscriptions
        unsubscribers.push(
            registerEventHandler(
                'onTrackSubscribed',
                (participant: RemoteParticipant, track: RemoteAudioTrack) => {
                    if (audioTrackManagerRef.current) {
                        // Find participant data from the participants map
                        const participantData = participants.get(
                            participant.identity
                        )
                        if (participantData) {
                            audioTrackManagerRef.current.setMasterTrack(
                                track,
                                participantData
                            )
                        }
                    }
                }
            )
        )

        // Handle track unsubscriptions
        unsubscribers.push(
            registerEventHandler(
                'onTrackUnsubscribed',
                (participant: RemoteParticipant, _track: RemoteAudioTrack) => {
                    const participantData = participants.get(
                        participant.identity
                    )
                    if (
                        participantData?.type === 'interpreter' &&
                        audioTrackManagerRef.current
                    ) {
                        audioTrackManagerRef.current.removeInterpreterTrackIfActive(
                            participantData.participantId
                        )
                    }
                }
            )
        )

        // Handle participant disconnections
        unsubscribers.push(
            registerEventHandler(
                'onParticipantDisconnected',
                (participantId: string) => {
                    const participantData = participants.get(participantId)
                    if (
                        participantData?.type === 'interpreter' &&
                        audioTrackManagerRef.current
                    ) {
                        audioTrackManagerRef.current.removeInterpreterTrackIfActive(
                            participantData.participantId
                        )
                    }
                }
            )
        )

        return () => {
            unsubscribers.forEach((unsubscribe) => unsubscribe())
        }
    }, [registerEventHandler, participants])

    const handleAudioPermissionRequired = (callback: () => void) => {
        setShowAudioPermissionToast(true)
        setOnClickToListen(() => callback)
    }

    const handlePlaybackStarted = () => {
        setIsInterfaceDisabled(false)
    }

    return (
        <div className="min-h-screen flex justify-center items-center p-5 bg-gradient-to-br from-blue-500 to-purple-600 font-sans">
            <Card
                className={`
  w-full max-w-lg p-8 bg-white rounded-2xl shadow-2xl transition-all duration-300
  ${isInterfaceDisabled ? 'opacity-50 grayscale pointer-events-none' : ''}
  `}
            >
                <RoomHeader
                    roomTitle={roomDetails?.title}
                    roomDescription={roomDetails?.description}
                />

                <AudioTrackManager
                    ref={audioTrackManagerRef}
                    onAudioPermissionRequired={handleAudioPermissionRequired}
                    onPlaybackStarted={handlePlaybackStarted}
                    onActiveLanguageChange={setActiveLanguage}
                />

                <RoomManager participants={participants} />

                {/* Connection Status Indicator */}
                {(connectionStatus === 'connecting' || roomLookupLoading) && (
                    <div className="text-center text-blue-600 mb-4">
                        <div className="animate-spin inline-block w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full mr-2"></div>
                        {roomLookupLoading
                            ? 'Looking up room...'
                            : 'Connecting to live stream...'}
                    </div>
                )}

                {connectionStatus === 'error' && error && (
                    <div className="text-center text-red-600 mb-4 p-3 bg-red-50 rounded-lg">
                        ❌ {error}
                    </div>
                )}
            </Card>

            <AudioPermissionToast
                show={showAudioPermissionToast}
                onClick={onClickToListen}
            />

            <Toaster />
        </div>
    )
}
