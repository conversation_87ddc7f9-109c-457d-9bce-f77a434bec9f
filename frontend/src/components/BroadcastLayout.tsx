import { ReactNode } from 'react'
import { UserMenu } from './UserMenu'

interface BroadcastLayoutProps {
    children: ReactNode
    title?: string
    onTitleClick?: () => void
}

export function BroadcastLayout({
    children,
    title,
    onTitleClick
}: BroadcastLayoutProps) {
    return (
        <div className="min-h-screen bg-gradient-background">
            {/* Sticky Header */}
            <div className="sticky top-0 z-50 bg-gradient-card backdrop-blur-md border-b border-border/50">
                <div className={`max-w-6xl mx-auto px-4 md:px-6 py-4`}>
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <div className="w-3 h-3 bg-dashboard-primary rounded-full animate-pulse"></div>
                            <button
                                onClick={onTitleClick}
                                className="text-foreground font-bold text-lg hover:text-primary transition-colors"
                            >
                                VoiceFrom Go
                            </button>
                            {title && (
                                <>
                                    <span className="text-muted-foreground">
                                        /
                                    </span>
                                    <span className="text-foreground font-bold text-lg">
                                        {title}
                                    </span>
                                </>
                            )}
                        </div>
                        <UserMenu />
                    </div>
                </div>
            </div>

            <div className={`max-w-6xl mx-auto space-y-6 p-4 md:p-6`}>
                {children}
            </div>
        </div>
    )
}
