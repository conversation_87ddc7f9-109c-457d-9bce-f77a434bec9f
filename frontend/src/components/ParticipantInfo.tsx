import { Button } from '@/components/ui/Button'
import { ParticipantData } from '@/types/reactTypes'

interface ParticipantInfoProps {
    participant: ParticipantData
    isActive?: boolean
    onSelect?: () => void
}

export function ParticipantInfo({
    participant,
    isActive = false,
    onSelect
}: ParticipantInfoProps) {
    const handleClick = () => {
        if (onSelect) {
            onSelect()
        }
    }

    return (
        <Button
            variant={isActive ? 'default' : 'outline'}
            size="lg"
            onClick={handleClick}
            className={`
        flex items-center gap-3 px-6 py-4 h-auto
        transition-all duration-200 ease-in-out
        ${
            isActive
                ? 'bg-blue-500 text-white shadow-lg scale-105'
                : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300'
        }
      `}
        >
            <span className="text-xl" role="img" aria-label="Language flag">
                {participant.flagEmoji}
            </span>
            <span className="font-medium text-sm">
                {participant.displayName}
            </span>
        </Button>
    )
}
