/**
 * BroadcastTestApp - Minimal container for the broadcast test flow
 *
 * Mirrors the auth gating pattern used in BroadcastApp.tsx:
 * - If unauthenticated: render AuthForm
 * - If authenticated: render RoomBroadcastTestComponent
 */

import { RoomBroadcastTestComponent } from './RoomBroadcastTestComponent'
import { useAuth } from '@/hooks/useAuth'
import { AuthForm } from './AuthForm'

export function BroadcastTestApp() {
    const {
        authState,
        user,
        signIn,
        signInWithGoogle,
        signUp,
        loading,
        authenticatedFetch,
        signOut
    } = useAuth()

    // If not authenticated, show auth form
    if (authState !== 'authenticated') {
        return (
            <AuthForm
                signIn={signIn}
                signInWithGoogle={() => signInWithGoogle('/broadcast-test')}
                signUp={(email, password, confirmPassword) =>
                    signUp(email, password, confirmPassword, '/broadcast-test')
                }
                loading={loading}
            />
        )
    }

    // Authenticated: render the test component
    return (
        <RoomBroadcastTestComponent
            authenticatedFetch={authenticatedFetch}
            userId={user!.id}
            userEmail={user!.email}
            signOut={signOut}
        />
    )
}
