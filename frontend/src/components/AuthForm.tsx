/**
 * AuthForm Component
 *
 * A self-contained authentication form that handles both signin and signup.
 * Uses the useAuth hook directly and includes Google OAuth, email/password authentication,
 * password visibility toggles, and toast notifications.
 */

import React, { useState } from 'react'
import { toast } from 'react-toastify'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Separator } from '@/components/ui/Separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Label } from '@/components/ui/Label'
import { Input } from '@/components/ui/Input'
import { Mail, Lock, Eye, EyeOff } from 'lucide-react'
import { logger } from '@/logger'

interface AuthFormProps {
    signIn: (email: string, password: string) => Promise<string | undefined>
    signInWithGoogle: (redirectPath?: string) => Promise<string | undefined>
    signUp: (
        email: string,
        password: string,
        confirmPassword: string,
        redirectPath?: string
    ) => Promise<string | undefined>
    loading: boolean
}

export function AuthForm({
    signIn,
    signInWithGoogle,
    signUp,
    loading: authLoading
}: AuthFormProps) {
    // Auth form state
    const [authForm, setAuthForm] = useState({
        email: '',
        password: ''
    })

    // Additional auth state for signup
    const [signupForm, setSignupForm] = useState({
        email: '',
        password: '',
        confirmPassword: ''
    })

    // Password visibility state
    const [passwordVisibility, setPasswordVisibility] = useState({
        signin: false,
        signup: false,
        confirmPassword: false
    })

    const handleSignIn = async (e: React.FormEvent) => {
        e.preventDefault()
        toast.dismiss() // Dismiss any existing toasts

        const error = await signIn(authForm.email, authForm.password)

        if (error) {
            toast.error(error)
            logger.error('Failed to sign in:', error)
        } else {
            toast.success('Welcome back! You have successfully signed in.')
        }
    }

    const handleSignUp = async (e: React.FormEvent) => {
        e.preventDefault()
        toast.dismiss() // Dismiss any existing toasts

        const error = await signUp(
            signupForm.email,
            signupForm.password,
            signupForm.confirmPassword
        )

        if (error) {
            toast.error(error)
            logger.error('Failed to sign up:', error)
        } else {
            setSignupForm((prev) => ({
                ...prev,
                password: '',
                confirmPassword: ''
            }))

            toast.info(
                'Please check your email for a confirmation link to complete your registration.'
            )
        }
    }

    const handleGoogleSignIn = async () => {
        toast.dismiss() // Dismiss any existing toasts

        const error = await signInWithGoogle()

        if (error) {
            toast.error(error)
            logger.error('Failed to sign in with Google:', error)
        }
    }

    return (
        <div className="min-h-screen bg-gradient-background flex items-center justify-center p-4">
            <Card className="w-full max-w-lg bg-gradient-card border-0 shadow-broadcast backdrop-blur-sm">
                <CardHeader className="text-center pb-8">
                    <CardTitle className="text-3xl font-bold bg-gradient-primary bg-clip-text text-transparent mb-2">
                        Live Interpretation Platform
                    </CardTitle>
                    <p className="text-muted-foreground text-base">
                        Access your broadcasting dashboard
                    </p>
                </CardHeader>
                <CardContent className="px-8 pb-8">
                    <div className="mb-6">
                        <Button
                            variant="outline"
                            className="w-full"
                            onClick={handleGoogleSignIn}
                            disabled={authLoading}
                        >
                            <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                                <path
                                    fill="currentColor"
                                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                                />
                                <path
                                    fill="currentColor"
                                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                                />
                                <path
                                    fill="currentColor"
                                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                                />
                                <path
                                    fill="currentColor"
                                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                                />
                            </svg>
                            {authLoading
                                ? 'Connecting...'
                                : 'Continue with Google'}
                        </Button>
                    </div>

                    <div className="relative mb-6">
                        <div className="absolute inset-0 flex items-center">
                            <Separator className="w-full" />
                        </div>
                        <div className="relative flex justify-center text-xs uppercase">
                            <span className="bg-card px-2 text-muted-foreground">
                                Or
                            </span>
                        </div>
                    </div>

                    <Tabs defaultValue="signin" className="w-full">
                        <TabsList className="grid w-full grid-cols-2">
                            <TabsTrigger value="signin">Sign In</TabsTrigger>
                            <TabsTrigger value="signup">Sign Up</TabsTrigger>
                        </TabsList>

                        <TabsContent value="signin">
                            <form onSubmit={handleSignIn} className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="email">Email</Label>
                                    <div className="relative">
                                        <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="email"
                                            type="email"
                                            placeholder="Enter your email"
                                            value={authForm.email}
                                            onChange={(e) =>
                                                setAuthForm((prev) => ({
                                                    ...prev,
                                                    email: e.target.value
                                                }))
                                            }
                                            className="pl-10"
                                            required
                                        />
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="password">Password</Label>
                                    <div className="relative">
                                        <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="password"
                                            type={
                                                passwordVisibility.signin
                                                    ? 'text'
                                                    : 'password'
                                            }
                                            placeholder="Enter your password"
                                            value={authForm.password}
                                            onChange={(e) =>
                                                setAuthForm((prev) => ({
                                                    ...prev,
                                                    password: e.target.value
                                                }))
                                            }
                                            className="pl-10 pr-10"
                                            required
                                        />
                                        <button
                                            type="button"
                                            onClick={() =>
                                                setPasswordVisibility(
                                                    (prev) => ({
                                                        ...prev,
                                                        signin: !prev.signin
                                                    })
                                                )
                                            }
                                            className="absolute right-3 top-3 h-4 w-4 text-muted-foreground hover:text-foreground transition-colors"
                                        >
                                            {passwordVisibility.signin ? (
                                                <EyeOff className="h-4 w-4" />
                                            ) : (
                                                <Eye className="h-4 w-4" />
                                            )}
                                        </button>
                                    </div>
                                </div>

                                <Button
                                    type="submit"
                                    className="w-full shadow-button-soft bg-auth-primary hover:bg-auth-primary/90 text-auth-primary-foreground"
                                    disabled={authLoading}
                                >
                                    {authLoading ? 'Signing in...' : 'Sign In'}
                                </Button>
                            </form>
                        </TabsContent>

                        <TabsContent value="signup">
                            <form onSubmit={handleSignUp} className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="signup-email">Email</Label>
                                    <div className="relative">
                                        <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="signup-email"
                                            type="email"
                                            placeholder="Enter your email"
                                            value={signupForm.email}
                                            onChange={(e) =>
                                                setSignupForm((prev) => ({
                                                    ...prev,
                                                    email: e.target.value
                                                }))
                                            }
                                            className="pl-10"
                                            required
                                        />
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="signup-password">
                                        Password
                                    </Label>
                                    <div className="relative">
                                        <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="signup-password"
                                            type={
                                                passwordVisibility.signup
                                                    ? 'text'
                                                    : 'password'
                                            }
                                            placeholder="Create a password"
                                            value={signupForm.password}
                                            onChange={(e) =>
                                                setSignupForm((prev) => ({
                                                    ...prev,
                                                    password: e.target.value
                                                }))
                                            }
                                            className="pl-10 pr-10"
                                            required
                                            minLength={6}
                                        />
                                        <button
                                            type="button"
                                            onClick={() =>
                                                setPasswordVisibility(
                                                    (prev) => ({
                                                        ...prev,
                                                        signup: !prev.signup
                                                    })
                                                )
                                            }
                                            className="absolute right-3 top-3 h-4 w-4 text-muted-foreground hover:text-foreground transition-colors"
                                        >
                                            {passwordVisibility.signup ? (
                                                <EyeOff className="h-4 w-4" />
                                            ) : (
                                                <Eye className="h-4 w-4" />
                                            )}
                                        </button>
                                    </div>
                                    <p className="text-xs text-muted-foreground">
                                        Must be at least 6 characters long
                                    </p>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="confirm-password">
                                        Confirm Password
                                    </Label>
                                    <div className="relative">
                                        <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="confirm-password"
                                            type={
                                                passwordVisibility.confirmPassword
                                                    ? 'text'
                                                    : 'password'
                                            }
                                            placeholder="Confirm your password"
                                            value={signupForm.confirmPassword}
                                            onChange={(e) =>
                                                setSignupForm((prev) => ({
                                                    ...prev,
                                                    confirmPassword:
                                                        e.target.value
                                                }))
                                            }
                                            className="pl-10 pr-10"
                                            required
                                            minLength={6}
                                        />
                                        <button
                                            type="button"
                                            onClick={() =>
                                                setPasswordVisibility(
                                                    (prev) => ({
                                                        ...prev,
                                                        confirmPassword:
                                                            !prev.confirmPassword
                                                    })
                                                )
                                            }
                                            className="absolute right-3 top-3 h-4 w-4 text-muted-foreground hover:text-foreground transition-colors"
                                        >
                                            {passwordVisibility.confirmPassword ? (
                                                <EyeOff className="h-4 w-4" />
                                            ) : (
                                                <Eye className="h-4 w-4" />
                                            )}
                                        </button>
                                    </div>
                                </div>

                                <Button
                                    type="submit"
                                    className="w-full shadow-button-soft bg-auth-primary hover:bg-auth-primary/90 text-auth-primary-foreground"
                                    disabled={authLoading}
                                >
                                    {authLoading
                                        ? 'Creating account...'
                                        : 'Create Account'}
                                </Button>
                            </form>
                        </TabsContent>
                    </Tabs>
                </CardContent>
            </Card>
        </div>
    )
}
