import { logger } from '@/logger'
import {
    useState,
    useRef,
    useCallback,
    useEffect,
    forwardRef,
    useImperativeHandle
} from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Slider } from '@/components/ui/Slider'
import { Button } from '@/components/ui/Button'
import { Volume2, VolumeX } from 'lucide-react'
import { ParticipantData, AudioTrack } from '@/types/reactTypes'
import { RemoteAudioTrack } from 'livekit-client'

interface AudioTrackManagerProps {
    onAudioPermissionRequired?: (callback: () => void) => void
    onPlaybackStarted?: () => void
    onActiveLanguageChange?: (language: string | null) => void
}

export interface AudioTrackManagerRef {
    setMasterTrack: (
        track: RemoteAudioTrack,
        participant: ParticipantData
    ) => void
    removeInterpreterTrackIfActive: (participantId: string) => void
}

export const AudioTrackManager = forwardRef<
    AudioTrackManagerRef,
    AudioTrackManagerProps
>(
    (
        {
            onAudioPermissionRequired,
            onPlaybackStarted,
            onActiveLanguageChange
        },
        ref
    ) => {
        const [masterVolume, setMasterVolume] = useState([100])
        const [secondaryVolume, setSecondaryVolume] = useState([7])
        const [masterLabel, setMasterLabel] = useState('')
        const [secondaryLabel, setSecondaryLabel] = useState('')
        const [hasSecondaryTrack, setHasSecondaryTrack] = useState(false)
        const [isMasterMuted, setIsMasterMuted] = useState(false)
        const [isSecondaryMuted, setIsSecondaryMuted] = useState(false)
        const [userHasGrantedPermission, setUserHasGrantedPermission] =
            useState(false)
        const broadcasterTrackRef = useRef<AudioTrack | null>(null)
        const interpreterTrackRef = useRef<AudioTrack | null>(null)
        const hiddenContainerRef = useRef<HTMLDivElement>(null)

        const getMasterAudioElement =
            useCallback((): HTMLAudioElement | null => {
                if (
                    interpreterTrackRef.current &&
                    broadcasterTrackRef.current
                ) {
                    return interpreterTrackRef.current.element
                } else if (broadcasterTrackRef.current) {
                    return broadcasterTrackRef.current.element
                }
                return null
            }, [])

        const getSecondaryAudioElement =
            useCallback((): HTMLAudioElement | null => {
                if (
                    interpreterTrackRef.current &&
                    broadcasterTrackRef.current
                ) {
                    return broadcasterTrackRef.current.element
                }
                return null
            }, [])

        const applyVolumeSettings = useCallback(() => {
            const masterAudio = getMasterAudioElement()
            const secondaryAudio = getSecondaryAudioElement()

            if (masterAudio) {
                masterAudio.volume = masterVolume[0] / 100
                masterAudio.muted = isMasterMuted
            }

            if (secondaryAudio) {
                secondaryAudio.volume = secondaryVolume[0] / 100
                secondaryAudio.muted = isSecondaryMuted
            }
        }, [
            masterVolume,
            secondaryVolume,
            isMasterMuted,
            isSecondaryMuted,
            getMasterAudioElement,
            getSecondaryAudioElement
        ])

        // Updates the active language based on the current interpreter track state
        // Called whenever track roles change to keep language in sync
        const updateActiveLanguage = useCallback(() => {
            const language =
                interpreterTrackRef.current?.participant.language || null
            onActiveLanguageChange?.(language)
        }, [onActiveLanguageChange])

        const assignTrackRoles = useCallback(() => {
            if (interpreterTrackRef.current && broadcasterTrackRef.current) {
                setMasterLabel('AI Interpreter')
                setSecondaryLabel('Original Audio')
                setHasSecondaryTrack(true)
            } else if (broadcasterTrackRef.current) {
                setMasterLabel('Original Audio')
                setSecondaryLabel('')
                setHasSecondaryTrack(false)
            } else {
                setMasterLabel('')
                setSecondaryLabel('')
                setHasSecondaryTrack(false)
            }
            // Update active language whenever track roles change
            updateActiveLanguage()
            applyVolumeSettings()
        }, [applyVolumeSettings, updateActiveLanguage])

        const attemptAutoplay = useCallback(() => {
            const masterAudioElement = getMasterAudioElement()
            if (!masterAudioElement) return

            masterAudioElement
                .play()
                .then(() => {
                    logger.debug('🎧 AUDIO TRACK MANAGER: Autoplay successful')
                    if (onPlaybackStarted) {
                        onPlaybackStarted()
                    }
                })
                .catch((error) => {
                    logger.error(
                        '🎧 AUDIO TRACK MANAGER: Autoplay failed',
                        error
                    )
                    if (onAudioPermissionRequired) {
                        onAudioPermissionRequired(() => {
                            const masterAudioElement = getMasterAudioElement()
                            if (masterAudioElement) {
                                masterAudioElement.muted = false
                                masterAudioElement.play()
                            }

                            const secondaryAudioElement =
                                getSecondaryAudioElement()
                            if (secondaryAudioElement) {
                                secondaryAudioElement.muted = false
                                secondaryAudioElement.play()
                            }

                            setUserHasGrantedPermission(true)
                            if (onPlaybackStarted) {
                                onPlaybackStarted()
                            }
                        })
                    }
                })
        }, [
            getMasterAudioElement,
            getSecondaryAudioElement,
            onAudioPermissionRequired,
            onPlaybackStarted
        ])

        const setMasterTrack = useCallback(
            (track: RemoteAudioTrack, participant: ParticipantData) => {
                logger.debug(
                    '🎵 Setting master track for:',
                    participant.participantId,
                    participant.type
                )

                const audioElement = track.attach() as HTMLAudioElement
                audioElement.style.display = 'none'

                if (!userHasGrantedPermission) {
                    audioElement.autoplay = false
                    audioElement.muted = true
                } else {
                    audioElement.autoplay = true
                    audioElement.muted = false
                }

                if (hiddenContainerRef.current) {
                    hiddenContainerRef.current.appendChild(audioElement)
                }

                const audioTrack: AudioTrack = {
                    track,
                    element: audioElement,
                    participant
                }

                if (participant.type === 'broadcaster') {
                    broadcasterTrackRef.current = audioTrack
                    logger.debug('🎙️ Set broadcaster track')
                } else if (participant.type === 'interpreter') {
                    if (interpreterTrackRef.current) {
                        logger.debug('🔄 Replacing existing interpreter track')
                        const oldElement = interpreterTrackRef.current.element
                        oldElement.muted = true
                        oldElement.pause()
                        if (oldElement.parentNode) {
                            oldElement.parentNode.removeChild(oldElement)
                        }
                    }
                    interpreterTrackRef.current = audioTrack
                    logger.debug(
                        '🌍 Set interpreter track for language:',
                        participant.language
                    )
                }

                assignTrackRoles()

                audioElement.addEventListener('canplay', () => {
                    logger.debug('🎵 Audio track can play, attempting autoplay')
                    attemptAutoplay()
                })

                if (audioElement.readyState >= 3) {
                    logger.debug(
                        '🎵 Audio track ready, attempting immediate play'
                    )
                    attemptAutoplay()
                }
            },
            [userHasGrantedPermission, assignTrackRoles, attemptAutoplay]
        )

        const removeInterpreterTrackIfActive = useCallback(
            (participantId: string) => {
                if (
                    interpreterTrackRef.current?.participant.participantId ===
                    participantId
                ) {
                    logger.debug(
                        '🗑️ Removing interpreter track:',
                        participantId
                    )
                    const element = interpreterTrackRef.current.element
                    element.muted = true
                    element.pause()
                    if (element.parentNode) {
                        element.parentNode.removeChild(element)
                    }
                    interpreterTrackRef.current = null
                    assignTrackRoles()
                }
            },
            [assignTrackRoles]
        )

        useEffect(() => {
            applyVolumeSettings()
        }, [applyVolumeSettings])

        useImperativeHandle(
            ref,
            () => ({
                setMasterTrack,
                removeInterpreterTrackIfActive
            }),
            [setMasterTrack, removeInterpreterTrackIfActive]
        )

        const handleMasterVolumeChange = (value: number[]) => {
            setMasterVolume(value)
            if (value[0] > 0 && isMasterMuted) {
                setIsMasterMuted(false)
            }
        }

        const handleSecondaryVolumeChange = (value: number[]) => {
            setSecondaryVolume(value)
            if (value[0] > 0 && isSecondaryMuted) {
                setIsSecondaryMuted(false)
            }
        }

        const toggleMasterMute = () => {
            setIsMasterMuted(!isMasterMuted)
        }

        const toggleSecondaryMute = () => {
            setIsSecondaryMuted(!isSecondaryMuted)
        }

        return (
            <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-800 mb-5 flex items-center gap-2">
                    <span>🎵</span>
                    Audio Controls
                </h3>

                <Card className="mb-5 bg-blue-50 border-blue-200">
                    <CardHeader className="pb-4">
                        <div className="flex justify-between items-center">
                            <CardTitle className="text-base text-blue-700">
                                Master Volume
                            </CardTitle>
                            <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold min-w-[48px] text-center">
                                {masterVolume[0]}%
                            </span>
                        </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                        <div className="flex items-center gap-3">
                            <Slider
                                value={masterVolume}
                                onValueChange={handleMasterVolumeChange}
                                max={100}
                                step={1}
                                className="flex-1"
                            />
                            <Button
                                variant={isMasterMuted ? 'outline' : 'default'}
                                size="icon"
                                onClick={toggleMasterMute}
                                className="rounded-full"
                                title={isMasterMuted ? 'Unmute' : 'Mute'}
                            >
                                {isMasterMuted ? (
                                    <VolumeX className="h-4 w-4" />
                                ) : (
                                    <Volume2 className="h-4 w-4" />
                                )}
                            </Button>
                        </div>
                        <div className="mt-3 text-center">
                            <em className="text-sm text-gray-600">
                                Currently: {masterLabel}
                            </em>
                        </div>
                    </CardContent>
                </Card>

                {hasSecondaryTrack && (
                    <Card className="mb-5 bg-gray-50 border-gray-200">
                        <CardHeader className="pb-4">
                            <div className="flex justify-between items-center">
                                <CardTitle className="text-base text-gray-700">
                                    Secondary Volume
                                </CardTitle>
                                <span className="bg-gray-500 text-white px-3 py-1 rounded-full text-sm font-semibold min-w-[48px] text-center">
                                    {secondaryVolume[0]}%
                                </span>
                            </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                            <div className="flex items-center gap-3">
                                <Slider
                                    value={secondaryVolume}
                                    onValueChange={handleSecondaryVolumeChange}
                                    max={100}
                                    step={1}
                                    className="flex-1"
                                />
                                <Button
                                    variant={
                                        isSecondaryMuted ? 'outline' : 'default'
                                    }
                                    size="icon"
                                    onClick={toggleSecondaryMute}
                                    className="rounded-full"
                                    title={isSecondaryMuted ? 'Unmute' : 'Mute'}
                                >
                                    {isSecondaryMuted ? (
                                        <VolumeX className="h-4 w-4" />
                                    ) : (
                                        <Volume2 className="h-4 w-4" />
                                    )}
                                </Button>
                            </div>
                            <div className="mt-3 text-center">
                                <em className="text-sm text-gray-600">
                                    Currently: {secondaryLabel}
                                </em>
                            </div>
                        </CardContent>
                    </Card>
                )}

                <div ref={hiddenContainerRef} style={{ display: 'none' }} />
            </div>
        )
    }
)

AudioTrackManager.displayName = 'AudioTrackManager'
