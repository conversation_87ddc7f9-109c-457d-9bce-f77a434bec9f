import { useOutletContext, useBlocker } from 'react-router-dom'
import { Card, CardContent } from '@/components/ui/Card'
import { buildListenUrl } from '@/lib/url'
import {
    BroadcastHeader,
    AudioSourceButton,
    EndSessionButton,
    StopBroadcastButton,
    EndSessionConfirmDialog
} from './dashboard/broadcast'
import { toast } from 'react-toastify'
import type { SessionContext } from './SessionComponent'
import { AudioSource, useBroadcast } from '@/hooks/useBroadcast'
import { useEffect, useState } from 'react'
import { copyToClipboard } from '@/lib/clipboard'

export function ControllerComponent() {
    const { session, leaveAndEndSession } = useOutletContext<SessionContext>()

    const { startBroadcast, stopBroadcast, broadcastState } = useBroadcast()

    const [audioSource, setAudioSource] = useState<AudioSource | null>(null)

    const [endDialogOpen, setEndDialogOpen] = useState(false)

    const isIdle = broadcastState === 'idle'
    const isPreparing = broadcastState === 'preparing'
    const isBroadcasting = broadcastState === 'broadcasting'
    const isStopping = broadcastState === 'stopping'

    const listenUrl = buildListenUrl(session.url_identifier!)

    const blocker = useBlocker(isBroadcasting)

    const handleStartBroadcast = async (selectedSource: AudioSource) => {
        toast.dismiss()

        setAudioSource(selectedSource)

        try {
            await startBroadcast(session, selectedSource)

            toast.success(
                `Now broadcasting from ${selectedSource === 'microphone' ? 'microphone' : 'browser audio'}`
            )
        } catch (error) {
            toast.error('Failed to start broadcasting')
            setAudioSource(null)
        }
    }

    const handleStopBroadcast = async () => {
        try {
            await stopBroadcast()
            setAudioSource(null)

            toast.success('The broadcast has been stopped')
        } catch (error) {
            toast.error('Failed to stop broadcasting')
        }
    }

    useEffect(() => {
        if (blocker.state === 'blocked') {
            handleStopBroadcast()
            blocker.proceed()
        }
    }, [blocker])

    return (
        <div className="space-y-6">
            <Card className="bg-gradient-card border-0 shadow-broadcast backdrop-blur-sm">
                <BroadcastHeader
                    roomTitle={session.room_title ?? 'Untitled Room'}
                    shareUrl={buildListenUrl(session.url_identifier!)}
                    onCopy={() =>
                        copyToClipboard(
                            listenUrl,
                            'Share link has been copied to clipboard'
                        )
                    }
                    isBroadcasting={isBroadcasting}
                    audioSource={audioSource}
                />

                <CardContent className="space-y-6">
                    {isIdle || isPreparing ? (
                        <>
                            <div className="w-full h-px bg-border/50"></div>

                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold text-foreground">
                                    Audio Source
                                </h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <AudioSourceButton
                                        icon="mic"
                                        description="Use your microphone for live speech"
                                        sourceType="microphone"
                                        isIdle={isIdle}
                                        isPreparing={isPreparing}
                                        selectedAudioSource={audioSource}
                                        onStart={() =>
                                            handleStartBroadcast('microphone')
                                        }
                                    />

                                    <AudioSourceButton
                                        icon="monitor"
                                        description="Share audio from a Chrome tab"
                                        sourceType="browser_audio"
                                        isIdle={isIdle}
                                        isPreparing={isPreparing}
                                        selectedAudioSource={audioSource}
                                        onStart={() =>
                                            handleStartBroadcast('browser_audio')
                                        }
                                    />
                                </div>
                            </div>

                            <EndSessionButton
                                onClick={() => setEndDialogOpen(true)}
                                disabled={isPreparing}
                            />

                            <EndSessionConfirmDialog
                                isOpen={endDialogOpen}
                                onOpenChange={setEndDialogOpen}
                                onConfirm={leaveAndEndSession}
                            />
                        </>
                    ) : (
                        <StopBroadcastButton
                            onClick={handleStopBroadcast}
                            disabled={isStopping}
                            isStopping={isStopping}
                        />
                    )}
                </CardContent>
            </Card>
        </div>
    )
}
