/**
 * Shared language constants that match the backend Language enum
 * Keep this in sync with the Language enum in broadcaster.py
 * TypeScript version of language-constants.js
 */

export interface Language {
    code: string
    name: string
    flag: string
}

export const LANGUAGES: Language[] = [
    { code: 'ar', name: 'Arabic', flag: '🇸🇦' },
    { code: 'de', name: 'German', flag: '🇩🇪' },
    { code: 'de-DE', name: 'German (Germany)', flag: '🇩🇪' },
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'en-US', name: 'English (US)', flag: '🇺🇸' },
    { code: 'en-GB', name: 'English (UK)', flag: '🇬🇧' },
    { code: 'es', name: 'Spanish', flag: '🇪🇸' },
    { code: 'es-ES', name: 'Spanish (Spain)', flag: '🇪🇸' },
    { code: 'es-US', name: 'Spanish (US)', flag: '🇺🇸' },
    { code: 'fr', name: 'French', flag: '🇫🇷' },
    { code: 'fr-FR', name: 'French (France)', flag: '🇫🇷' },
    { code: 'hi', name: 'Hindi', flag: '🇮🇳' },
    { code: 'hi-IN', name: 'Hindi (India)', flag: '🇮🇳' },
    { code: 'id', name: 'Indonesian', flag: '🇮🇩' },
    { code: 'it', name: 'Italian', flag: '🇮🇹' },
    { code: 'it-IT', name: 'Italian (Italy)', flag: '🇮🇹' },
    { code: 'pl', name: 'Polish', flag: '🇵🇱' },
    { code: 'pl-PL', name: 'Polish (Poland)', flag: '🇵🇱' },
    { code: 'pt', name: 'Portuguese', flag: '🇵🇹' },
    { code: 'pt-PT', name: 'Portuguese (Portugal)', flag: '🇵🇹' },
    { code: 'pt-BR', name: 'Portuguese (Brazil)', flag: '🇧🇷' },
    { code: 'ru', name: 'Russian', flag: '🇷🇺' },
    { code: 'ru-RU', name: 'Russian (Russia)', flag: '🇷🇺' }
]

/**
 * Get language name by code
 * @param code - Language code (e.g., 'en-US', 'es')
 * @returns Language name or the code if not found
 */
export function getLanguageName(code: string): string {
    const lang = LANGUAGES.find((l) => l.code === code)
    return lang ? lang.name : code
}

/**
 * Get language object by code
 * @param code - Language code
 * @returns Language object or null if not found
 */
export function getLanguage(code: string): Language | null {
    return LANGUAGES.find((l) => l.code === code) || null
}

/**
 * Get all language codes
 * @returns Array of language codes
 */
export function getLanguageCodes(): string[] {
    return LANGUAGES.map((l) => l.code)
}

/**
 * Check if a language code is valid
 * @param code - Language code to validate
 * @returns True if valid, false otherwise
 */
export function isValidLanguageCode(code: string): boolean {
    return LANGUAGES.some((l) => l.code === code)
}
