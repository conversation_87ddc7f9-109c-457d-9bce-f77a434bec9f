import { Card, CardContent, CardHeader } from '../ui/Card'
import { SessionDetails } from '@/hooks/useSessionManager'

interface SessionCardProps {
    session: SessionDetails
    onClick?: (session: SessionDetails) => void
    showFeedback?: boolean
}

export function SessionCard({ session, onClick }: SessionCardProps) {
    const formatDate = (dateString?: string): string => {
        if (!dateString) return ''
        const date = new Date(dateString)
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        })
    }

    // const formatDurationInMinutes = (duration: string): string => {
    //     const durationMatch = duration.match(/(?:(\d+)h\s*)?(?:(\d+)m)?/)
    //     if (!durationMatch) return '0 minutes'
    //     const hours = parseInt(durationMatch[1] || '0')
    //     const minutes = parseInt(durationMatch[2] || '0')
    //     const totalMinutes = hours * 60 + minutes
    //     return `${totalMinutes} minutes`
    // }

    // const calculateListenerTime = (duration: string, totalListeners: number): string => {
    //     const durationMatch = duration.match(/(?:(\d+)h\s*)?(?:(\d+)m)?/)
    //     if (!durationMatch) return '0 listener minutes'
    //     const hours = parseInt(durationMatch[1] || '0')
    //     const minutes = parseInt(durationMatch[2] || '0')
    //     const totalMinutes = hours * 60 + minutes
    //     const listenerMinutes = totalMinutes * totalListeners
    //     if (listenerMinutes >= 1440) {
    //         const days = Math.floor(listenerMinutes / 1440)
    //         return `${days} listener day${days !== 1 ? 's' : ''}`
    //     } else if (listenerMinutes >= 60) {
    //         const listenerHours = Math.floor(listenerMinutes / 60)
    //         return `${listenerHours} listener hour${listenerHours !== 1 ? 's' : ''}`
    //     } else {
    //         return `${listenerMinutes} listener minute${listenerMinutes !== 1 ? 's' : ''}`
    //     }
    // }

    const isClickable = session.session_status === 'CREATED'
    const cardClassName = `bg-background/50 border border-border/50 ${isClickable ? 'cursor-pointer hover:bg-background/70 transition-colors' : ''}`

    return (
        <Card
            className={cardClassName}
            onClick={() => isClickable && onClick?.(session)}
        >
            <CardHeader className="pb-3">
                <div className="space-y-1">
                    <div className="flex items-baseline justify-between gap-2">
                        <h3 className="font-semibold text-lg mr-2">
                            {session.session_title ?? session.room_title}
                        </h3>
                        <div className="flex items-center gap-2">
                            {session.session_status === 'CREATED' && (
                                <div className="flex items-center gap-2 bg-warning/10 rounded-lg px-3 py-1">
                                    <div className="w-2 h-2 bg-warning rounded-full animate-pulse-soft"></div>
                                    <span className="text-xs font-medium text-warning">
                                        READY
                                    </span>
                                </div>
                            )}
                        </div>
                    </div>
                    <p className="text-sm text-muted-foreground">
                        {session.session_title ? (
                            <>
                                {session.room_title} |{' '}
                                {formatDate(session.session_created_at)}
                            </>
                        ) : (
                            <>{formatDate(session.session_created_at)}</>
                        )}
                    </p>
                </div>
            </CardHeader>
            <CardContent className="space-y-3">
                {/* {session.status !== 'scheduled' && (
                    <div className="grid grid-cols-2 gap-3 text-sm">
                        {session.status === 'completed' ? (
                            <>
                                <div className="flex items-center gap-2">
                                    <Globe className="w-4 h-4 text-muted-foreground" />
                                    <span>{session.languages} languages</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Users className="w-4 h-4 text-muted-foreground" />
                                    <span>0 listeners</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock className="w-4 h-4 text-muted-foreground" />
                                    <span>{formatDurationInMinutes(session.duration)}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Timer className="w-4 h-4 text-muted-foreground" />
                                    <span>{calculateListenerTime(session.duration, session.totalListeners)}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <TrendingUp className="w-4 h-4 text-muted-foreground" />
                                    <span>{session.estimatedCost}</span>
                                </div>
                            </>
                        ) : (
                            <>
                                <div className="flex items-center gap-2">
                                    <Clock className="w-4 h-4 text-muted-foreground" />
                                    <span>{session.duration}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Users className="w-4 h-4 text-muted-foreground" />
                                    <span>{session.totalListeners}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Timer className="w-4 h-4 text-muted-foreground" />
                                    <span>{calculateListenerTime(session.duration, session.totalListeners)}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Globe className="w-4 h-4 text-muted-foreground" />
                                    <span>{session.languages} languages</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <TrendingUp className="w-4 h-4 text-muted-foreground" />
                                    <span>{session.estimatedCost}</span>
                                </div>
                            </>
                        )}
                    </div>
                )} */}

                {/* Session Feedback for completed sessions */}
                {/* {showFeedback && session.status === 'completed' && (
                    <SessionFeedback
                        sessionId={session.session_id}
                        sessionName={session.room_title}
                        sessionDate={session.session_created_at}
                    />
                )} */}
            </CardContent>
        </Card>
    )
}
