// import React from 'react'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { Button } from '../ui/Button'
import { Play, Calendar, Globe, Plus, Radio, CheckCircle } from 'lucide-react'
import { CreateRoomDialog } from './CreateRoomDialog'
import { Dialog, DialogTrigger } from '../ui/Dialog'
import { RoomCard } from './RoomCard'
import { AuthenticatedFetch } from '@/hooks/useAuth'
import { useRoomManager } from '@/hooks/useRoomManager'
import { useSessionManager, SessionDetails } from '@/hooks/useSessionManager'
import type { Room } from '@/types/room'
import { Skeleton } from '../ui/Skeleton'
import { CreateSessionDialog } from './CreateSessionDialog'
import { SessionCard } from './SessionCard'
import { logger } from '@/logger'
// import { StatisticsCards } from './StatisticCards'
import { toast } from 'react-toastify'

export interface DashboardProps {
    authenticatedFetch: AuthenticatedFetch
    userId: string
    onNavigateToBroadcast: (session: SessionDetails) => void
}

export function Dashboard({
    authenticatedFetch,
    userId,
    onNavigateToBroadcast
}: DashboardProps) {
    // Local state
    const [isCreateRoomOpen, setIsCreateRoomOpen] = useState<boolean>(false)
    const [isCreateSessionOpen, setIsCreateSessionOpen] =
        useState<boolean>(false)

    const [rooms, setRooms] = useState<Room[]>([])
    const { getRoomList, loading: isLoadingRooms } = useRoomManager(
        authenticatedFetch,
        userId
    )

    const fetchRooms = useCallback(async () => {
        try {
            const data = await getRoomList()
            setRooms(data)
        } catch (err) {
            logger.error('Failed to fetch rooms', err)
            toast.error(`Failed to fetch rooms`)
        }
    }, [getRoomList])
    useEffect(() => {
        fetchRooms()
    }, [fetchRooms])

    const [sessions, setSessions] = useState<SessionDetails[]>([])
    const { getSessionList, loading: isLoadingSessions } = useSessionManager(
        authenticatedFetch,
        userId
    )

    const fetchSessions = useCallback(async () => {
        try {
            const data = await getSessionList()
            setSessions(data)
        } catch (err) {
            console.error('Failed to fetch sessions', err)
            toast.error(`Failed to fetch sessions`)
        }
    }, [getSessionList])
    useEffect(() => {
        fetchSessions()
    }, [fetchSessions])

    const availableSessions = useMemo(
        () => sessions.filter((s) => s.session_status === 'CREATED'),
        [sessions]
    )
    const completedSessions = useMemo(
        () => sessions.filter((s) => s.session_status === 'COMPLETED'),
        [sessions]
    )

    const onRoomCreated = async () => {
        await fetchRooms()
    }

    const onSessionCreated = async (session: SessionDetails) => {
        onNavigateToBroadcast(session)
    }

    const onSessionClick = async (session: SessionDetails) => {
        logger.info('Session clicked', session)
        if (!session.url_identifier) {
            toast.error('Session URL identifier not found')
            return
        }
        onNavigateToBroadcast(session)
    }

    const renderSessionSkeletons = () => (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3].map((i) => (
                <Card
                    key={i}
                    className="bg-background/50 border border-border/50"
                >
                    <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                            <div className="space-y-2 flex-1">
                                <Skeleton className="h-5 w-3/4" />
                                <Skeleton className="h-4 w-1/2" />
                            </div>
                            <Skeleton className="h-6 w-16" />
                        </div>
                    </CardHeader>
                    <CardContent className="space-y-3">
                        <div className="grid grid-cols-2 gap-3">
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-full" />
                        </div>
                        <div className="pt-2 border-t border-border/50">
                            <Skeleton className="h-4 w-24" />
                        </div>
                    </CardContent>
                </Card>
            ))}
        </div>
    )

    const renderNoActiveSessions = () => (
        <div className="text-center py-12">
            <div className="mx-auto w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mb-4">
                <Radio className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold mb-2">No Active Sessions</h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                Create a new broadcasting session to start live interpretation
                events.
            </p>
            <Button
                variant="dashboard"
                onClick={() => setIsCreateSessionOpen(true)}
                className="shadow-button-soft"
            >
                <Plus className="w-4 h-4 mr-2" />
                Create Session
            </Button>
        </div>
    )

    const renderNoCompletedSessions = () => (
        <div className="text-center py-12">
            <div className="mx-auto w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mb-4">
                <CheckCircle className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold mb-2">
                No Completed Sessions
            </h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                Once you complete broadcasting sessions, they will appear here
                with analytics and performance metrics.
            </p>
        </div>
    )

    const renderNoRooms = () => (
        <div className="text-center py-8 sm:py-12 px-4">
            <div className="mx-auto w-12 h-12 sm:w-16 sm:h-16 bg-gradient-primary rounded-full flex items-center justify-center mb-3 sm:mb-4">
                <Globe className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
            </div>
            <h3 className="text-lg sm:text-xl font-semibold mb-2">
                No Rooms Created
            </h3>
            <p className="text-muted-foreground mb-4 sm:mb-6 max-w-md mx-auto text-sm sm:text-base">
                Create your first room to start organizing your broadcasting
                sessions and share listening URLs.
            </p>
            <Button
                variant="dashboard"
                onClick={() => setIsCreateRoomOpen(true)}
                className="shadow-button-soft text-sm sm:text-base px-4 sm:px-6"
                size="sm"
            >
                <Plus className="w-4 h-4 mr-2" />
                Create Room
            </Button>
        </div>
    )

    return (
        <>
            {/* Create Session Dialog */}
            <CreateSessionDialog
                isOpen={isCreateSessionOpen}
                authenticatedFetch={authenticatedFetch}
                userId={userId}
                onOpenChange={async (open) => setIsCreateSessionOpen(open)}
                rooms={rooms}
                onSessionCreated={onSessionCreated}
            />

            {/* Active & Ready Sessions */}
            <Card className="bg-gradient-card border-0 shadow-card-soft backdrop-blur-sm">
                <CardHeader>
                    <div className="flex items-center justify-between">
                        <CardTitle className="flex items-center gap-2">
                            <Play className="w-5 h-5 text-dashboard-primary" />
                            Active & Ready Sessions
                        </CardTitle>
                        {availableSessions.length > 0 && (
                            <Dialog
                                open={isCreateSessionOpen}
                                onOpenChange={setIsCreateSessionOpen}
                            >
                                <DialogTrigger asChild>
                                    <Button variant="dashboard">
                                        <Plus className="w-4 h-4 mr-2" />
                                        Create Session
                                    </Button>
                                </DialogTrigger>
                            </Dialog>
                        )}
                    </div>
                </CardHeader>
                <CardContent>
                    {isLoadingSessions ? (
                        renderSessionSkeletons()
                    ) : availableSessions.length === 0 ? (
                        renderNoActiveSessions()
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {availableSessions.map((session) => (
                                <SessionCard
                                    key={session.session_id}
                                    session={session}
                                    onClick={onSessionClick}
                                />
                            ))}
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Completed Sessions */}
            <Card className="bg-gradient-card border-0 shadow-card-soft backdrop-blur-sm">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Calendar className="w-5 h-5 text-dashboard-primary" />
                        Completed Sessions
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {/* Statistics Cards */}
                    {/* <div className="mb-8">
                            <StatisticsCards sessions={sessions} />
                            <div className="border-t border-border/50 pt-6">
                                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                                    <CheckCircle className="w-5 h-5 text-dashboard-primary" />
                                    Session History
                                </h3>
                            </div>
                        </div> */}
                    {isLoadingSessions ? (
                        renderSessionSkeletons()
                    ) : completedSessions.length === 0 ? (
                        renderNoCompletedSessions()
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {completedSessions.map((session) => (
                                <SessionCard
                                    key={session.session_id}
                                    session={session}
                                    showFeedback={true}
                                />
                            ))}
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Create Room Dialog */}
            <CreateRoomDialog
                isOpen={isCreateRoomOpen}
                authenticatedFetch={authenticatedFetch}
                userId={userId}
                onOpenChange={setIsCreateRoomOpen}
                onRoomCreated={onRoomCreated}
            />

            {/* Existing Rooms */}
            <Card className="bg-gradient-card border-0 shadow-card-soft backdrop-blur-sm">
                <CardHeader className="pb-4 sm:pb-6">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-0">
                        <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                            <Globe className="w-4 h-4 sm:w-5 sm:h-5 text-dashboard-primary" />
                            Rooms
                        </CardTitle>
                        {rooms.length > 0 && (
                            <Dialog
                                open={isCreateRoomOpen}
                                onOpenChange={setIsCreateRoomOpen}
                            >
                                <DialogTrigger asChild>
                                    <Button
                                        variant="dashboard"
                                        size="sm"
                                        className="w-full sm:w-auto"
                                    >
                                        <Plus className="w-4 h-4 mr-2" />
                                        <span className="sm:inline">
                                            Create Room
                                        </span>
                                    </Button>
                                </DialogTrigger>
                            </Dialog>
                        )}
                    </div>
                </CardHeader>
                <CardContent>
                    {isLoadingRooms ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {[1, 2].map((i) => (
                                <Card
                                    key={i}
                                    className="bg-background/50 border border-border/50"
                                >
                                    <CardContent className="p-4">
                                        <div className="space-y-3">
                                            <div className="space-y-1">
                                                <Skeleton className="h-5 w-3/4" />
                                                <Skeleton className="h-4 w-1/2" />
                                            </div>
                                            <Skeleton className="h-8 w-full" />
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    ) : rooms.length === 0 ? (
                        renderNoRooms()
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {rooms.map((room) => (
                                <RoomCard key={room.id} room={room} />
                            ))}
                        </div>
                    )}
                </CardContent>
            </Card>
        </>
    )
}
