import { Card, CardContent } from '../ui/Card'
import { Button } from '../ui/Button'
import { Copy } from 'lucide-react'
import { buildListenUrl } from '@/lib/url'
import { Room } from '@/types/room'
import { Badge } from '../ui/Badge'
import { formatDate } from '@/lib/date'
import { copyToClipboard } from '@/lib/clipboard'

interface RoomCardProps {
    room: Room
}

export function RoomCard({ room }: RoomCardProps) {
    const listenUrl = buildListenUrl(room.url_identifier)

    return (
        <Card
            className={`bg-background/50 border ${room.status === 'BOOKED' ? 'border-warning/50 bg-warning/5' : 'border-border/50'}`}
        >
            <CardContent className="p-4">
                <div className="flex items-center justify-between">
                    <div className="space-y-1 flex-1">
                        <h3 className="font-semibold flex items-center gap-2">
                            {room.title}
                            {room.status === 'BOOKED' && (
                                <Badge
                                    variant="outline"
                                    className="text-xs bg-warning/10 text-warning border-warning/30"
                                >
                                    In Use
                                </Badge>
                            )}
                        </h3>
                        <p className="text-sm text-muted-foreground">
                            Created {formatDate(room.created_at)}
                        </p>
                        <div className="flex items-center gap-2">
                            <p className="text-xs text-muted-foreground font-mono bg-muted/50 rounded px-2 py-1 flex-1">
                                {listenUrl}
                            </p>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                    copyToClipboard(
                                        listenUrl,
                                        'Room URL copied to clipboard'
                                    )
                                }
                            >
                                <Copy className="w-4 h-4" />
                            </Button>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    )
}
