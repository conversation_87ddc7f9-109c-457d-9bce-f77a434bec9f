import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '../ui/Dialog'
import { Button } from '../ui/Button'
import { Input } from '../ui/Input'
import { Label } from '../ui/Label'
import { toast } from 'react-toastify'
import { logger } from '../../logger'
import { CreateRoomParams, useRoomManager } from '@/hooks/useRoomManager'
import { AuthenticatedFetch } from '@/hooks/useAuth'
import { useUrlValidator } from '@/hooks/useUrlValidator'
import { useDerivedIdentifier } from '@/hooks/useDerivedIdentifier'
import { buildListenUrl } from '@/lib/url'

interface CreateRoomDialogProps {
    isOpen: boolean
    authenticatedFetch: AuthenticatedFetch
    userId: string
    onOpenChange: (open: boolean) => void
    onRoomCreated: () => void
}

export function CreateRoomDialog({
    isOpen,
    authenticatedFetch,
    userId,
    onO<PERSON><PERSON>hang<PERSON>,
    onRoomCreated
}: CreateRoomDialogProps) {
    // Form state for room creation
    const [roomForm, setRoomForm] = useState<CreateRoomParams>({
        title: '',
        description: null,
        url_identifier: null
    })

    // Derived identifier management (auto-derive until manual edit)
    const { onManualEdit, resetManualEdit } = useDerivedIdentifier({
        title: roomForm.title,
        setIdentifier: (identifier: string) => {
            if (roomForm.url_identifier !== identifier) {
                setIsAvailable(null)
                setRoomForm((prev) => ({ ...prev, url_identifier: identifier }))
            }
        }
    })

    const { isChecking, checkIdentifierAvailability } = useUrlValidator()

    const [isAvailable, setIsAvailable] = useState<boolean | null>(null)

    const { createRoom, loading: roomLoading } = useRoomManager(
        authenticatedFetch,
        userId
    )

    const hasIdentifier = !!(
        roomForm.url_identifier && roomForm.url_identifier.trim()
    )
    const isCreateDisabled =
        roomLoading || (hasIdentifier && isAvailable !== true)

    // Debounced identifier check
    useEffect(() => {
        const timer = setTimeout(() => {
            if (roomForm.url_identifier) {
                checkIdentifierAvailability(roomForm.url_identifier)
                    .then(setIsAvailable)
                    .catch((err: unknown) => {
                        const code = err instanceof Error ? err.message : ''
                        if (code === 'EMPTY_IDENTIFIER') {
                            setIsAvailable(null)
                        } else if (code === 'INVALID_IDENTIFIER') {
                            setIsAvailable(false)
                        } else {
                            setIsAvailable(false)
                        }
                    })
            } else {
                setIsAvailable(null)
            }
        }, 500)
        return () => clearTimeout(timer)
    }, [roomForm.url_identifier, checkIdentifierAvailability])

    const handleCreateRoom = async () => {
        toast.dismiss()

        if (!roomForm.title.trim()) {
            toast.error('Room name is required')
            return
        }

        if (
            roomForm.url_identifier &&
            roomForm.url_identifier.trim() &&
            isAvailable !== true
        ) {
            toast.error('Please choose a valid and available URL identifier')
            return
        }

        try {
            await createRoom(roomForm)
            onRoomCreated()
            toast.success(`"${roomForm.title}" has been created successfully`)

            // Reset form
            setRoomForm({
                title: '',
                description: null,
                url_identifier: null
            })
            setIsAvailable(null)
            resetManualEdit()
            onOpenChange(false)
        } catch (error) {
            logger.error('Error creating room:', error)
            toast.error('Failed to create room')
        }
    }

    const handleClose = () => {
        onOpenChange(false)
        setRoomForm({
            title: '',
            description: null,
            url_identifier: null
        })
        setIsAvailable(null)
        resetManualEdit()
    }

    return (
        <Dialog
            open={isOpen}
            onOpenChange={(open: boolean) => {
                if (roomLoading) return
                onOpenChange(open)
            }}
        >
            <DialogContent
                onEscapeKeyDown={(e: any) => {
                    if (roomLoading) e.preventDefault()
                }}
                onPointerDownOutside={(e: any) => {
                    if (roomLoading) e.preventDefault()
                }}
                onInteractOutside={(e: any) => {
                    if (roomLoading) e.preventDefault()
                }}
                closeDisabled={roomLoading}
            >
                <DialogHeader>
                    <DialogTitle>Create New Room</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="room-name">Room Name</Label>
                        <Input
                            id="room-name"
                            placeholder="e.g., Tech Conference 2024"
                            value={roomForm.title}
                            onChange={(e) =>
                                setRoomForm((prev) => ({
                                    ...prev,
                                    title: e.target.value
                                }))
                            }
                            className="focus-visible:ring-dashboard-primary"
                            disabled={roomLoading}
                        />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="url-identifier">
                            URL Identifier (Optional)
                        </Label>
                        <div className="flex items-center">
                            <span className="text-sm text-muted-foreground mr-2">
                                listen/
                            </span>
                            <Input
                                id="url-identifier"
                                placeholder="some-identifier-123"
                                className={`flex-1 ${
                                    isAvailable === false
                                        ? 'border-destructive focus-visible:ring-destructive'
                                        : isAvailable === true
                                          ? 'border-success focus-visible:ring-success'
                                          : 'focus-visible:ring-dashboard-primary'
                                }`}
                                value={roomForm.url_identifier || ''}
                                onChange={(e) => {
                                    setIsAvailable(null)
                                    onManualEdit(e.target.value)
                                }}
                                disabled={roomLoading}
                            />
                        </div>
                        {roomForm.url_identifier && (
                            <div className="flex items-center gap-2 text-xs">
                                {isChecking && (
                                    <span className="text-muted-foreground">
                                        Checking availability...
                                    </span>
                                )}
                                {!isChecking && isAvailable === true && (
                                    <span className="text-success">
                                        ✓ Available
                                    </span>
                                )}
                                {!isChecking && isAvailable === false && (
                                    <span className="text-destructive">
                                        ✗ Unavailable
                                    </span>
                                )}
                            </div>
                        )}
                        {roomForm.url_identifier && (
                            <p className="text-xs text-muted-foreground">
                                Preview:{' '}
                                {buildListenUrl(roomForm.url_identifier)}
                            </p>
                        )}
                        <p className="text-xs text-muted-foreground">
                            Custom identifier for the listening URL. Use
                            letters, numbers, hyphens, and underscores (max 50
                            characters).
                        </p>
                    </div>
                    <div className="flex gap-2">
                        <Button
                            variant="outline"
                            onClick={handleClose}
                            className="flex-1"
                            disabled={roomLoading}
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="dashboard"
                            onClick={handleCreateRoom}
                            className="flex-1"
                            disabled={isCreateDisabled}
                            aria-busy={roomLoading}
                        >
                            {roomLoading ? (
                                <span className="inline-flex items-center">
                                    <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                                    Creating...
                                </span>
                            ) : (
                                'Create Room'
                            )}
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    )
}
