import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '../ui/Dialog'
import { Button } from '../ui/Button'
import { Input } from '../ui/Input'
import { Label } from '../ui/Label'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '../ui/Select'
import { toast } from 'react-toastify'
import { logger } from '@/logger'
import { CreateRoomParams, useRoomManager } from '@/hooks/useRoomManager'
import { SessionDetails, useSessionManager } from '@/hooks/useSessionManager'
import { useUrlValidator } from '@/hooks/useUrlValidator'
import { AuthenticatedFetch } from '@/hooks/useAuth'
import { Room } from '@/types/room'
import { useDerivedIdentifier } from '@/hooks/useDerivedIdentifier'
import { buildListenUrl } from '@/lib/url'

interface CreateSessionDialogProps {
    isOpen: boolean
    authenticatedFetch: AuthenticatedFetch
    userId: string
    onOpenChange: (open: boolean) => Promise<void>
    rooms: Room[]
    onSessionCreated: (session: SessionDetails) => Promise<void>
}

export function CreateSessionDialog({
    isOpen,
    authenticatedFetch,
    userId,
    onOpenChange,
    rooms,
    onSessionCreated
}: CreateSessionDialogProps) {
    // Form state for room creation
    const [roomForm, setRoomForm] = useState<CreateRoomParams>({
        title: '',
        description: null,
        url_identifier: null
    })
    const [selectedRoomId, setSelectedRoomId] = useState<number | null>(null)
    const [newSessionName, setNewSessionName] = useState('')
    const [roomSelectionError, setRoomSelectionError] = useState(false)
    const [activeTab, setActiveTab] = useState<'create' | 'select'>('select')

    const { createRoom, loading: roomLoading } = useRoomManager(
        authenticatedFetch,
        userId
    )

    const { createSession, loading: sessionLoading } = useSessionManager(
        authenticatedFetch,
        userId
    )

    const { isChecking, checkIdentifierAvailability } = useUrlValidator()

    const [isAvailable, setIsAvailable] = useState<boolean | null>(null)

    const combinedLoading = roomLoading || sessionLoading
    const isCreateTab = activeTab === 'create'
    const hasTitle = !!roomForm.title.trim()
    const identifierProvided = !!roomForm.url_identifier?.trim()
    const identifierValid = !identifierProvided || isAvailable === true

    const canSubmit = isCreateTab
        ? hasTitle && identifierValid
        : !!selectedRoomId
    const isActionDisabled = combinedLoading || !canSubmit

    // Derived identifier management (auto-derive until manual edit)
    const { onManualEdit, resetManualEdit } = useDerivedIdentifier({
        title: roomForm.title,
        setIdentifier: (identifier: string) => {
            if (roomForm.url_identifier !== identifier) {
                setIsAvailable(null)
                setRoomForm((prev) => ({ ...prev, url_identifier: identifier }))
            }
        }
    })

    // Debounced session room identifier check
    useEffect(() => {
        const timer = setTimeout(() => {
            if (roomForm.url_identifier) {
                checkIdentifierAvailability(roomForm.url_identifier)
                    .then(setIsAvailable)
                    .catch((err: unknown) => {
                        const code = err instanceof Error ? err.message : ''
                        if (code === 'EMPTY_IDENTIFIER') {
                            setIsAvailable(null)
                        } else if (code === 'INVALID_IDENTIFIER') {
                            setIsAvailable(false)
                        } else {
                            setIsAvailable(false)
                        }
                    })
            } else {
                setIsAvailable(null)
            }
        }, 500)
        return () => clearTimeout(timer)
    }, [roomForm.url_identifier, checkIdentifierAvailability])

    const handleCreateSession = async () => {
        toast.dismiss()

        // Validate room selection based on active tab
        if (activeTab === 'create') {
            if (!roomForm.title.trim()) {
                setRoomSelectionError(true)
                toast.error('Please enter a name for the new room')
                return
            }
        } else {
            if (!selectedRoomId) {
                setRoomSelectionError(true)
                toast.error(
                    'Please select a room for your broadcasting session'
                )
                return
            }
        }

        setRoomSelectionError(false)
        try {
            let roomId = selectedRoomId
            let roomName = ''

            // If creating a new room
            if (activeTab === 'create') {
                if (
                    roomForm.url_identifier &&
                    roomForm.url_identifier.trim() &&
                    isAvailable !== true
                ) {
                    toast.error(
                        'Please choose a valid and available URL identifier'
                    )
                    return
                }

                const roomDetails = await createRoom(roomForm)
                roomId = roomDetails.room_id || null
                roomName = roomDetails.title
            } else {
                const room = rooms.find((r) => r.id === selectedRoomId)
                roomName = room?.title || ''
            }

            if (!roomId) {
                toast.error('Please create or select a room')
                return
            }

            const session = await createSession({
                room_id: roomId,
                title: newSessionName.trim() ? newSessionName : null
            })
            onSessionCreated(session)

            toast.success(
                `"${newSessionName.trim() ? newSessionName : roomName}" broadcasting session has been created`
            )

            // Reset form
            setNewSessionName('')
            setSelectedRoomId(null)
            setRoomForm({
                title: '',
                description: null,
                url_identifier: null
            })
            setIsAvailable(null)
            resetManualEdit()
            onOpenChange(false)
        } catch (error) {
            logger.error('Error creating session:', error)
            toast.error('Failed to create session')
        }
    }

    const handleClose = () => {
        onOpenChange(false)
        setRoomSelectionError(false)
        setRoomForm({
            title: '',
            description: null,
            url_identifier: null
        })
        setIsAvailable(null)
        setSelectedRoomId(null)
        setNewSessionName('')
        setActiveTab('create')
        resetManualEdit()
    }

    return (
        <Dialog
            open={isOpen}
            onOpenChange={(open: boolean) => {
                if (combinedLoading) return
                onOpenChange(open)
            }}
        >
            <DialogContent
                onEscapeKeyDown={(e: any) => {
                    if (combinedLoading) e.preventDefault()
                }}
                onPointerDownOutside={(e: any) => {
                    if (combinedLoading) e.preventDefault()
                }}
                onInteractOutside={(e: any) => {
                    if (combinedLoading) e.preventDefault()
                }}
                closeDisabled={combinedLoading}
            >
                <DialogHeader>
                    <DialogTitle>Create Broadcasting Session</DialogTitle>
                </DialogHeader>
                <div className="space-y-8">
                    {/* Room Configuration Section */}
                    <div className="space-y-4">
                        <div className="flex items-center gap-2">
                            <div className="w-2 h-6 bg-dashboard-primary rounded-full"></div>
                            <h3 className="text-lg font-semibold">
                                Room Configuration
                            </h3>
                        </div>

                        {/* Tab Switcher */}
                        <div className="flex rounded-lg border p-1 bg-muted">
                            <button
                                type="button"
                                onClick={() => {
                                    setActiveTab('select')
                                    setRoomForm({
                                        title: '',
                                        description: null,
                                        url_identifier: null
                                    })
                                    setSelectedRoomId(null)
                                    setRoomSelectionError(false)
                                    resetManualEdit()
                                }}
                                disabled={combinedLoading}
                                className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                                    activeTab === 'select'
                                        ? 'bg-background text-foreground shadow-sm'
                                        : 'text-muted-foreground hover:text-foreground'
                                }`}
                            >
                                Select Existing Room
                            </button>
                            <button
                                type="button"
                                onClick={() => {
                                    setActiveTab('create')
                                    setSelectedRoomId(null)
                                    setRoomSelectionError(false)
                                    resetManualEdit()
                                }}
                                disabled={combinedLoading}
                                className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                                    activeTab === 'create'
                                        ? 'bg-background text-foreground shadow-sm'
                                        : 'text-muted-foreground hover:text-foreground'
                                }`}
                            >
                                Create New Room
                            </button>
                        </div>

                        {/* Tab Content */}
                        <div className="bg-muted/30 rounded-lg p-4 space-y-4">
                            {activeTab === 'select' ? (
                                <div className="space-y-2">
                                    <Label>Select Room *</Label>
                                    <Select
                                        value={selectedRoomId?.toString() || ''}
                                        onValueChange={(value: string) => {
                                            setSelectedRoomId(Number(value))
                                            setRoomSelectionError(false)
                                        }}
                                    >
                                        <SelectTrigger
                                            className={
                                                roomSelectionError
                                                    ? 'border-destructive focus-visible:ring-destructive'
                                                    : 'focus:ring-dashboard-primary'
                                            }
                                        >
                                            <SelectValue placeholder="Select an existing room" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {rooms
                                                .filter(
                                                    (room) =>
                                                        room.status !== 'BOOKED'
                                                )
                                                .map((room) => (
                                                    <SelectItem
                                                        key={room.id}
                                                        value={room.id.toString()}
                                                    >
                                                        <div className="flex items-center justify-between w-full">
                                                            <span>
                                                                {room.title}
                                                            </span>
                                                            <span className="text-xs text-muted-foreground/60 ml-2">
                                                                /
                                                                {
                                                                    room.url_identifier
                                                                }
                                                            </span>
                                                        </div>
                                                    </SelectItem>
                                                ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            ) : (
                                <div className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="new-room-name">
                                            Room Name *
                                        </Label>
                                        <Input
                                            id="new-room-name"
                                            placeholder="e.g., Conference Room A"
                                            value={roomForm.title}
                                            onChange={(e) => {
                                                const roomName = e.target.value
                                                setRoomForm({
                                                    ...roomForm,
                                                    title: roomName
                                                })
                                                setRoomSelectionError(false)
                                            }}
                                            className={`focus-visible:ring-dashboard-primary ${roomSelectionError ? 'border-destructive' : ''}`}
                                            disabled={combinedLoading}
                                        />
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="session-room-url-identifier">
                                            URL Identifier (Optional)
                                        </Label>
                                        <div className="flex items-center">
                                            <span className="text-sm text-muted-foreground mr-2">
                                                listen/
                                            </span>
                                            <Input
                                                id="session-room-url-identifier"
                                                placeholder="some-identifier-123"
                                                className={`flex-1 ${
                                                    isAvailable === false
                                                        ? 'border-destructive focus-visible:ring-destructive'
                                                        : isAvailable === true
                                                          ? 'border-success focus-visible:ring-success'
                                                          : 'focus-visible:ring-dashboard-primary'
                                                }`}
                                                value={
                                                    roomForm.url_identifier ||
                                                    ''
                                                }
                                                onChange={(e) => {
                                                    setIsAvailable(null)
                                                    onManualEdit(e.target.value)
                                                    setRoomSelectionError(false)
                                                }}
                                                disabled={combinedLoading}
                                            />
                                        </div>
                                        {roomForm.url_identifier && (
                                            <div className="flex items-center gap-2 text-xs">
                                                {isChecking && (
                                                    <span className="text-muted-foreground">
                                                        Checking availability...
                                                    </span>
                                                )}
                                                {!isChecking &&
                                                    isAvailable === true && (
                                                        <span className="text-success">
                                                            ✓ Available
                                                        </span>
                                                    )}
                                                {!isChecking &&
                                                    isAvailable === false && (
                                                        <span className="text-destructive">
                                                            ✗ Unavailable
                                                        </span>
                                                    )}
                                            </div>
                                        )}
                                        {roomForm.url_identifier && (
                                            <p className="text-xs text-muted-foreground">
                                                Preview:{' '}
                                                {buildListenUrl(
                                                    roomForm.url_identifier
                                                )}
                                            </p>
                                        )}
                                        <p className="text-xs text-muted-foreground">
                                            Custom identifier for the listening
                                            URL. Use letters, numbers, hyphens,
                                            and underscores (max 50 characters).
                                        </p>
                                    </div>
                                </div>
                            )}

                            {roomSelectionError && (
                                <p className="text-sm text-destructive">
                                    {activeTab === 'create'
                                        ? 'Please enter a room name to continue'
                                        : 'Please select a room to continue'}
                                </p>
                            )}
                        </div>
                    </div>

                    {/* Session Details Section */}
                    <div className="space-y-4">
                        <div className="flex items-center gap-2">
                            <div className="w-2 h-6 bg-blue-500 rounded-full"></div>
                            <h3 className="text-lg font-semibold">
                                Session Details
                            </h3>
                        </div>

                        <div className="bg-blue-50/50 dark:bg-blue-950/20 rounded-lg p-4 space-y-3">
                            <div className="space-y-2">
                                <Label htmlFor="session-name">
                                    Session Name (Optional)
                                </Label>
                                <Input
                                    id="session-name"
                                    placeholder="e.g., Conference Room Session"
                                    value={newSessionName}
                                    onChange={(e) =>
                                        setNewSessionName(e.target.value)
                                    }
                                    className="focus-visible:ring-dashboard-primary"
                                />
                            </div>
                        </div>
                    </div>
                    <div className="flex gap-2">
                        <Button
                            variant="outline"
                            onClick={handleClose}
                            className="flex-1"
                            disabled={combinedLoading}
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="dashboard"
                            onClick={handleCreateSession}
                            disabled={isActionDisabled}
                            aria-busy={combinedLoading}
                            className="flex-1"
                        >
                            {combinedLoading ? (
                                <span className="inline-flex items-center">
                                    <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                                    Creating...
                                </span>
                            ) : (
                                'Create Session'
                            )}
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    )
}
