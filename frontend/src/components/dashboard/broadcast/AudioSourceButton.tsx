import { Mic, <PERSON> } from 'lucide-react'
import { cn } from '@/lib/utils'
import type { AudioSource } from '@/hooks/useBroadcast'

interface AudioSourceButtonProps {
    icon: 'mic' | 'monitor'
    description: string
    sourceType: AudioSource
    isIdle: boolean
    isPreparing: boolean
    selectedAudioSource: AudioSource | null
    onStart: () => void
}

const LABELS: Record<AudioSource, string> = {
    microphone: 'Broadcast from Microphone',
    browser_audio: 'Broadcast from Chrome Tab'
}

export function AudioSourceButton({
    icon,
    description,
    sourceType,
    isIdle,
    isPreparing,
    selectedAudioSource,
    onStart
}: AudioSourceButtonProps) {
    const Icon = icon === 'mic' ? Mic : Monitor
    const isActive = isPreparing && selectedAudioSource === sourceType
    const disabled = !isIdle

    const handleClick = () => {
        if (!disabled) onStart()
    }

    const getTitle = () => {
        return isActive ? 'Starting broadcast...' : LABELS[sourceType]
    }

    const getClassName = () => {
        return cn(
            // Base styles
            'border-2 border-dashed border-dashboard-primary/30 rounded-lg p-6',
            'flex flex-col items-center justify-center gap-3',
            'transition-all duration-200 min-h-[140px]',

            // Interactive states
            isIdle && 'cursor-pointer hover:border-primary/50 hover:bg-primary/5',
            !isIdle && 'cursor-not-allowed opacity-50',

            // Active state
            isActive && 'border-primary bg-primary/10 animate-pulse'
        )
    }

    return (
        <div onClick={handleClick} className={getClassName()}>
            <Icon className="w-8 h-8 text-dashboard-primary" />
            <span className="text-dashboard-primary font-medium text-center">
                {getTitle()}
            </span>
            <span className="text-sm text-muted-foreground text-center">
                {description}
            </span>
        </div>
    )
}
