import { Mi<PERSON>, <PERSON> } from 'lucide-react'
import { cn } from '@/lib/utils'
import type { AudioSource } from '@/hooks/useBroadcast'

export function AudioSourceButton({
    icon,
    description,
    sourceType,
    isIdle,
    isPreparing,
    selectedAudioSource,
    onStart
}: {
    icon: 'mic' | 'monitor'
    description: string
    sourceType: AudioSource
    isIdle: boolean
    isPreparing: boolean
    selectedAudioSource: AudioSource | null
    onStart: () => void
}) {
    const Icon = icon === 'mic' ? Mic : Monitor

    const isThisSourcePreparing =
        isPreparing && selectedAudioSource === sourceType

    const baseClasses =
        'border-2 border-dashed border-dashboard-primary/30 rounded-lg p-6 flex flex-col items-center justify-center gap-3 transition-all duration-200 min-h-[140px]'
    const interactiveClasses = isIdle
        ? 'cursor-pointer hover:border-primary/50 hover:bg-primary/5'
        : 'cursor-not-allowed opacity-50'
    const preparingClasses = isThisSourcePreparing
        ? 'border-primary bg-primary/10 animate-pulse'
        : ''

    const className = cn(baseClasses, interactiveClasses, preparingClasses)

    const labels: Record<AudioSource, string> = {
        microphone: 'Broadcast from Microphone',
        browser_audio: 'Broadcast from Chrome Tab'
    }

    const title = isThisSourcePreparing
        ? 'Starting broadcast...'
        : labels[sourceType]

    const disabled = !isIdle
    return (
        <div onClick={() => !disabled && onStart()} className={className}>
            <Icon className="w-8 h-8 text-dashboard-primary" />
            <span className="text-dashboard-primary font-medium text-center">
                {title}
            </span>
            <span className="text-sm text-muted-foreground text-center">
                {description}
            </span>
        </div>
    )
}
