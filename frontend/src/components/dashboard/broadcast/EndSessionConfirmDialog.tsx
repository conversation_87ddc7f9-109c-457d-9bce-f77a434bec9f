import { useState } from 'react'
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle
} from '@/components/ui/Dialog'
import { Button } from '@/components/ui/Button'

interface EndSessionConfirmDialogProps {
    isOpen: boolean
    onOpenChange: (open: boolean) => void
    onConfirm: () => Promise<void> | void
    title?: string
    description?: string
    confirmText?: string
    cancelText?: string
}

export function EndSessionConfirmDialog({
    isOpen,
    onOpenChange,
    onConfirm,
    title = 'End session?',
    description = 'This will end the session for everyone. This action cannot be undone.',
    confirmText = 'End Session',
    cancelText = 'Cancel'
}: EndSessionConfirmDialogProps) {
    const [isConfirming, setIsConfirming] = useState(false)

    const handleConfirm = async () => {
        try {
            setIsConfirming(true)
            await onConfirm()
        } finally {
            setIsConfirming(false)
            onOpenChange(false)
        }
    }

    return (
        <Dialog
            open={isOpen}
            onOpenChange={(open) => {
                if (!isConfirming) onOpenChange(open)
            }}
        >
            <DialogContent
                onEscapeKeyDown={(e: any) => {
                    if (isConfirming) e.preventDefault()
                }}
                onPointerDownOutside={(e: any) => {
                    if (isConfirming) e.preventDefault()
                }}
                onInteractOutside={(e: any) => {
                    if (isConfirming) e.preventDefault()
                }}
                closeDisabled={isConfirming}
            >
                <DialogHeader>
                    <DialogTitle>{title}</DialogTitle>
                    <DialogDescription>{description}</DialogDescription>
                </DialogHeader>

                <DialogFooter>
                    <Button
                        variant="outline"
                        onClick={() => onOpenChange(false)}
                        disabled={isConfirming}
                    >
                        {cancelText}
                    </Button>
                    <Button
                        variant="destructive"
                        onClick={handleConfirm}
                        disabled={isConfirming}
                        aria-busy={isConfirming}
                    >
                        {isConfirming ? (
                            <span className="inline-flex items-center">
                                <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                                Ending...
                            </span>
                        ) : (
                            confirmText
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
