import { <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/Card'
import { But<PERSON> } from '@/components/ui/Button'
import { <PERSON><PERSON>, Mic, Monitor } from 'lucide-react'
import { AudioSource } from '@/hooks/useBroadcast'

export function BroadcastHeader({
    roomTitle,
    shareUrl,
    onCopy,
    isBroadcasting,
    audioSource
}: {
    roomTitle: string
    shareUrl: string
    onCopy: () => void
    isBroadcasting: boolean
    audioSource: AudioSource | null
}) {
    return (
        <CardHeader className="pb-3">
            <div className="relative flex items-center justify-between">
                <div className="space-y-1">
                    <CardTitle className="text-3xl font-bold">
                        Broadcasting Control Panel
                    </CardTitle>
                    <div className="flex items-center gap-3 pt-2">
                        <p className="text-muted-foreground text-lg">
                            {roomTitle}
                        </p>
                        <div className="flex items-center gap-2 bg-background/50 rounded-lg px-3 py-1">
                            <span className="text-sm text-muted-foreground truncate max-w-xs">
                                {shareUrl}
                            </span>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={onCopy}
                                className="h-6 w-6 p-0 hover:bg-primary/10"
                            >
                                <Copy className="w-3 h-3" />
                            </Button>
                        </div>
                    </div>
                </div>
                {isBroadcasting && (
                    <div
                        className={`absolute top-0 right-0 flex items-center gap-2 bg-success/10 rounded-lg px-3 py-1`}
                    >
                        <div
                            className={`w-2 h-2 bg-success rounded-full animate-pulse-soft`}
                        ></div>
                        <span className={`text-xs font-medium text-success`}>
                            LIVE
                        </span>
                    </div>
                )}
            </div>

            {isBroadcasting && (
                <div className="pt-2">
                    <div className="p-3 bg-background/30 rounded-lg border">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                                {audioSource === 'microphone' ? (
                                    <Mic className="w-4 h-4 text-dashboard-primary" />
                                ) : (
                                    <Monitor className="w-4 h-4 text-dashboard-primary" />
                                )}
                                <span className="text-sm font-medium">
                                    Broadcasting from:{' '}
                                    {audioSource === 'browser_audio'
                                        ? 'Chrome Tab'
                                        : 'Microphone'}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </CardHeader>
    )
}
