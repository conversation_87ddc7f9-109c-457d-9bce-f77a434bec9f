import { Button } from '@/components/ui/Button'
import { Square } from 'lucide-react'

export function StopBroadcastButton({
    onClick,
    disabled,
    isStopping
}: {
    onClick: () => void
    disabled: boolean
    isStopping: boolean
}) {
    return (
        <div className="space-y-6">
            <div className="w-full h-px bg-border/50"></div>
            <div className="space-y-4">
                <div className="flex items-center gap-4">
                    <Button
                        variant="destructive"
                        size="default"
                        onClick={onClick}
                        className="h-10 px-4 font-medium shadow-button-soft w-48 flex-shrink-0"
                        disabled={disabled}
                    >
                        <Square className="w-4 h-4 mr-2" />
                        {isStopping ? 'Stopping...' : 'Stop Broadcasting'}
                    </Button>
                </div>
            </div>
        </div>
    )
}
