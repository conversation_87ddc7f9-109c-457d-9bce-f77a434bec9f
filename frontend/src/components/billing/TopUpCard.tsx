import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import PaymentForm from './PaymentForm'
import { toast } from 'react-toastify'
import type { AuthenticatedFetch } from '@/hooks/useAuth'
import { useBilling } from '@/hooks/useBilling'
import { logger } from '@/logger'

interface TopUpCardProps {
    authenticatedFetch: AuthenticatedFetch
}

export function TopUpCard({ authenticatedFetch }: TopUpCardProps) {
    const { fetchBalance, createPaymentIntent } = useBilling(authenticatedFetch)

    // Component state
    const [selectedAmountUsd, setSelectedAmountUsd] = useState<number>(25)
    const [customAmountUsd, setCustomAmountUsd] = useState<string>('')
    const [currentBalance, setCurrentBalance] = useState<number>(0)
    const [balanceLoading, setBalanceLoading] = useState<boolean>(true)
    const [showPaymentForm, setShowPaymentForm] = useState<boolean>(false)
    const [pendingAmountUsd, setPendingAmountUsd] = useState<number>(0)
    const [clientSecret, setClientSecret] = useState<string>('')
    const [paymentInitializing, setPaymentInitializing] =
        useState<boolean>(false)

    const presetAmountsUsd = [10, 25, 50, 100]

    useEffect(() => {
        loadBalance()
    }, [])

    const validateAmountUsd = (
        amountUsd: number
    ): { valid: boolean; error?: string } => {
        if (amountUsd < 0.5) {
            return { valid: false, error: 'Minimum amount is $0.50' }
        }
        return { valid: true }
    }

    const getDisplayAmountUsd = (
        selectedAmountUsd: number,
        customAmountUsd: string
    ): number => {
        return customAmountUsd
            ? parseFloat(customAmountUsd) || 0
            : selectedAmountUsd
    }

    const loadBalance = async () => {
        setBalanceLoading(true)
        const balance = await fetchBalance((error) => {
            logger.error('Error fetching balance:', error)
            toast.error('Failed to load account balance')
        })
        if (balance !== undefined) {
            setCurrentBalance(balance)
        }
        setBalanceLoading(false)
    }

    const handleAmountUsdSelect = (amountUsd: number) => {
        setSelectedAmountUsd(amountUsd)
        setCustomAmountUsd(amountUsd.toString())
    }

    const handleCustomAmountUsdChange = (
        e: React.ChangeEvent<HTMLInputElement>
    ) => {
        const value = e.target.value
        setCustomAmountUsd(value)

        const numericValue = parseFloat(value)
        if (numericValue && numericValue > 0) {
            setSelectedAmountUsd(numericValue)
        } else if (!value) {
            setSelectedAmountUsd(25)
        }
    }

    const getCurrentDisplayAmountUsd = (): number => {
        return getDisplayAmountUsd(selectedAmountUsd, customAmountUsd)
    }

    const handleTopUp = async () => {
        const amount = getCurrentDisplayAmountUsd()

        const validation = validateAmountUsd(amount)
        if (!validation.valid) {
            toast.error(validation.error!)
            return
        }

        setPaymentInitializing(true)
        const secret = await createPaymentIntent(amount, (error) => {
            logger.error('Error creating payment intent:', error)
            toast.error(error.message || 'Failed to initialize payment')
        })

        if (secret) {
            setClientSecret(secret)
            setPendingAmountUsd(amount)
            setShowPaymentForm(true)
        }
        setPaymentInitializing(false)
    }

    const handlePaymentSuccess = () => {
        setShowPaymentForm(false)
        setPendingAmountUsd(0)
        setClientSecret('')
        loadBalance()
    }

    const handlePaymentCancel = () => {
        setShowPaymentForm(false)
        setPendingAmountUsd(0)
        setClientSecret('')
    }

    return (
        <>
            <Card className="bg-gradient-card border-0 shadow-card-soft backdrop-blur-sm">
                <CardHeader>
                    <div className="bg-slate-50 rounded-xl p-6 text-center mt-4">
                        <div className="text-gray-500 text-sm font-medium mb-2 uppercase tracking-wide">
                            Current Balance
                        </div>
                        <div className="text-5xl font-bold text-gray-800">
                            {balanceLoading ? (
                                <span className="text-gray-500">
                                    Loading...
                                </span>
                            ) : (
                                `$ ${currentBalance.toFixed(2)}`
                            )}
                        </div>
                    </div>
                </CardHeader>

                <CardContent className="space-y-8">
                    <div className="mb-8">
                        <div className="text-center font-semibold text-gray-800 mb-4">
                            Top up your account
                        </div>

                        <div className="grid grid-cols-2 gap-3 mb-6">
                            {presetAmountsUsd.map((amountUsd: number) => (
                                <Button
                                    key={amountUsd}
                                    variant="outline"
                                    className={`!bg-slate-50 !border-2 !border-gray-200 !text-gray-700 p-4 rounded-lg text-base font-semibold cursor-pointer transition-all h-auto text-center hover:!border-indigo-500 hover:!bg-blue-50 ${
                                        selectedAmountUsd === amountUsd
                                            ? '!border-indigo-500 !bg-indigo-500 !text-white'
                                            : ''
                                    }`}
                                    onClick={() =>
                                        handleAmountUsdSelect(amountUsd)
                                    }
                                    disabled={paymentInitializing}
                                >
                                    $ {amountUsd}
                                </Button>
                            ))}
                        </div>

                        <Input
                            type="number"
                            className="w-full p-4 border-2 border-gray-200 rounded-lg text-base mb-6 transition-colors focus:!border-indigo-500 focus:!outline-none"
                            placeholder="Or enter custom amount..."
                            min="1"
                            max="1000"
                            value={customAmountUsd}
                            onChange={handleCustomAmountUsdChange}
                            disabled={paymentInitializing}
                        />

                        <Button
                            className="!w-full !bg-indigo-500 !text-white !border-none p-4 rounded-lg text-base font-semibold cursor-pointer transition-colors hover:!bg-purple-700"
                            onClick={handleTopUp}
                            disabled={paymentInitializing}
                        >
                            {paymentInitializing
                                ? 'Initializing...'
                                : `Add $${getCurrentDisplayAmountUsd().toFixed(2)} Credit`}
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Payment Form Modal with React Stripe Elements */}
            {showPaymentForm && clientSecret && (
                <PaymentForm
                    amountUsd={pendingAmountUsd}
                    clientSecret={clientSecret}
                    onSuccess={handlePaymentSuccess}
                    onCancel={handlePaymentCancel}
                />
            )}
        </>
    )
}
