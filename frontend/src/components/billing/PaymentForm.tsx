// https://docs.stripe.com/sdks/stripejs-react?ui=embedded-components
import React, { useState } from 'react'
import { StripePaymentElementOptions, loadStripe } from '@stripe/stripe-js'
import {
    useStripe,
    useElements,
    PaymentElement,
    Elements
} from '@stripe/react-stripe-js'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { toast } from 'react-toastify'
import { logger } from '@/logger'

const stripePublishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY
if (!stripePublishableKey) {
    throw Error('VITE_STRIPE_PUBLISHABLE_KEY not set!')
}
// Initialize Stripe Promise outside component to avoid recreating
const stripePromise = loadStripe(stripePublishableKey)

interface CheckoutFormProps {
    amountUsd: number
    onSuccess: () => void
    onCancel: () => void
    paymentElementOptions?: StripePaymentElementOptions
}

/**
 * CheckoutForm Component
 *
 * Uses React Stripe.js components for payment processing.
 * This component is wrapped by Stripe's Elements provider which:
 * - Provides access to useStripe() and useElements() hooks
 * - Handles PaymentElement mounting/unmounting automatically
 * - Manages Stripe context and state
 */
function CheckoutForm({
    amountUsd,
    onSuccess,
    onCancel,
    paymentElementOptions
}: CheckoutFormProps) {
    const stripe = useStripe() // Hook to access Stripe instance
    const elements = useElements() // Hook to access Elements instance
    const [isProcessing, setIsProcessing] = useState(false)

    const handleSubmit = async (event: React.FormEvent) => {
        event.preventDefault()

        // Stripe.js has not yet loaded or Elements not ready
        if (!stripe || !elements) {
            toast.error('Payment system not ready. Please try again.')
            return
        }

        setIsProcessing(true)

        try {
            logger.debug('Confirming payment with React Stripe.js...')

            // Use the React Stripe.js confirmPayment method
            // This automatically uses the PaymentElement data
            const { error, paymentIntent } = await stripe.confirmPayment({
                elements,
                confirmParams: {
                    return_url: `${window.location.origin}/billing?payment=success`
                },
                redirect: 'if_required' // Only redirect for payment methods that require it
            })

            logger.debug('Stripe response:', { error, paymentIntent })

            if (error) {
                logger.error('Stripe error details:', error)
                toast.error(`Payment failed: ${error.message}`)
            } else if (paymentIntent && paymentIntent.status === 'succeeded') {
                toast.success(
                    `Payment successful! $${amountUsd.toFixed(2)} added to your account.`
                )
                onSuccess() // Close modal and reset state
            } else {
                logger.error(
                    'Unexpected payment status:',
                    paymentIntent?.status
                )
                toast.error(
                    'Payment status unclear. Please check your account.'
                )
            }
        } catch (error: any) {
            console.error('Payment error:', error)
            toast.error(error.message || 'Payment failed. Please try again.')
        } finally {
            setIsProcessing(false)
        }
    }

    return (
        <Card className="w-full max-w-md">
            <CardHeader>
                <CardTitle className="text-center">
                    Complete Payment - ${amountUsd.toFixed(2)}
                </CardTitle>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                    {/*
                        PaymentElement Component
                        - Automatically mounts/unmounts when component mounts/unmounts
                        - Displays saved payment methods if customer has any
                        - Shows card input form for new payments
                        - Handles all payment method types supported by Stripe
                        - No manual DOM manipulation required!
                    */}
                    <div className="border rounded-lg p-4">
                        <PaymentElement options={paymentElementOptions} />
                    </div>

                    <div className="flex gap-3">
                        <Button
                            type="button"
                            variant="outline"
                            className="flex-1"
                            onClick={onCancel}
                            disabled={isProcessing}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            className="flex-1"
                            disabled={isProcessing || !stripe || !elements}
                        >
                            {isProcessing ? 'Processing...' : 'Pay Now'}
                        </Button>
                    </div>
                </form>
            </CardContent>
        </Card>
    )
}

interface PaymentFormProps {
    amountUsd: number
    clientSecret: string
    onSuccess: () => void
    onCancel: () => void
}

/**
 * PaymentForm Component with Modal
 *
 * This wrapper component:
 * 1. Wraps the CheckoutForm with Stripe Elements provider
 * 2. Includes the modal overlay and positioning
 * 3. Passes the clientSecret to Elements for saved payment method support
 */
function PaymentForm({
    amountUsd,
    clientSecret,
    onSuccess,
    onCancel
}: PaymentFormProps) {
    // Elements options with clientSecret for saved payment methods
    const elementsOptions = {
        clientSecret,
        appearance: {
            theme: 'stripe' as const
        }
    }

    const paymentElementOptions: StripePaymentElementOptions = {
        layout: 'tabs'
    }

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <Elements stripe={stripePromise} options={elementsOptions}>
                <CheckoutForm
                    amountUsd={amountUsd}
                    onSuccess={onSuccess}
                    onCancel={onCancel}
                    paymentElementOptions={paymentElementOptions}
                />
            </Elements>
        </div>
    )
}

export default PaymentForm
