import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card'
import { AuthenticatedFetch } from '@/hooks/useAuth'
import { useBilling, PaymentHistoryItem } from '@/hooks/useBilling'
import type { PaymentStatus } from '@/hooks/useBilling'
import { toast } from 'react-toastify'
import { logger } from '@/logger'
import { CreditCard } from 'lucide-react'

interface PaymentHistoryCardProps {
    authenticatedFetch: AuthenticatedFetch
}

export function PaymentHistoryCard({
    authenticatedFetch
}: PaymentHistoryCardProps) {
    const { fetchPaymentHistory } = useBilling(authenticatedFetch)
    const [loading, setLoading] = useState<boolean>(true)
    const [payments, setPayments] = useState<PaymentHistoryItem[]>([])

    useEffect(() => {
        loadPaymentHistory()
    }, [])

    const formatDate = (timestamp: number) => {
        return new Date(timestamp * 1000).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        })
    }

    const getStatusBadgeClass = (status: PaymentStatus) => {
        switch (status) {
            case 'SUCCEEDED':
                return 'bg-green-100 text-green-800'
            case 'PROCESSING':
                return 'bg-orange-100 text-orange-800'
            case 'CANCELED':
                return 'bg-red-100 text-red-800'
            default:
                return 'bg-gray-100 text-gray-800'
        }
    }

    const loadPaymentHistory = async () => {
        setLoading(true)
        const response = await fetchPaymentHistory((error) => {
            logger.error('Error fetching payment history:', error)
            toast.error('Failed to load payment history')
        })

        if (response) {
            setPayments(response.payments)
        }
        setLoading(false)
    }

    return (
        <Card className="bg-gradient-card border-0 shadow-card-soft backdrop-blur-sm">
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <CreditCard className="w-5 h-5 text-dashboard-primary" />
                    Payment History
                </CardTitle>
            </CardHeader>

            <CardContent>
                {loading ? (
                    <div className="text-center py-8">
                        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
                        <p className="text-gray-500 mt-2">
                            Loading payment history...
                        </p>
                    </div>
                ) : payments.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                        No payment history found
                    </div>
                ) : (
                    <div className="overflow-x-auto">
                        <table className="w-full">
                            {/* Table header with column names */}
                            <thead>
                                <tr className="bg-white border-b-2 border-gray-100 text-sm font-semibold text-gray-600 uppercase tracking-wide">
                                    <th className="text-left py-4 px-4">
                                        Date
                                    </th>
                                    <th className="text-left py-4 px-4">
                                        Type
                                    </th>
                                    <th className="text-left py-4 px-4">
                                        Platform Type
                                    </th>
                                    <th className="text-left py-4 px-4">
                                        Status
                                    </th>
                                    {/* align monatry amounts to the right for readability */}
                                    <th className="text-right py-4 px-4">
                                        Amount
                                    </th>
                                </tr>
                            </thead>
                            {/* Payment rows */}
                            <tbody>
                                {payments.map((payment) => (
                                    <tr
                                        key={payment.id}
                                        className="border-b border-gray-50 hover:bg-white"
                                    >
                                        <td className="py-4 px-4 text-gray-800">
                                            {formatDate(payment.created)}
                                        </td>
                                        <td className="py-4 px-4">
                                            {/* All payments are credit reloads */}
                                            <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs font-semibold">
                                                RELOAD
                                            </span>
                                        </td>
                                        <td className="py-4 px-4 text-gray-800">
                                            STRIPE
                                        </td>
                                        <td className="py-4 px-4">
                                            {/* Status badge with dynamic styling */}
                                            <span
                                                className={`px-3 py-1 rounded-full text-xs font-semibold ${getStatusBadgeClass(payment.status)}`}
                                            >
                                                {payment.status ?? 'UNKOWN'}
                                            </span>
                                        </td>
                                        {/* Amount with + prefix to show credit addition */}
                                        <td className="py-4 px-4 text-right text-gray-800 font-semibold">
                                            + ${payment.amount_usd.toFixed(2)}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </CardContent>
        </Card>
    )
}
