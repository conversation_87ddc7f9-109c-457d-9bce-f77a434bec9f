import { logger } from '@/logger'
import { useState, useEffect } from 'react'
import { ParticipantInfo } from '@/components/ParticipantInfo'
import { ParticipantData } from '@/types/reactTypes'

interface RoomManagerProps {
    participants: Map<string, ParticipantData>
}

export function RoomManager({ participants }: RoomManagerProps) {
    const [activeParticipantId, setActiveParticipantId] = useState<
        string | null
    >(null)

    const interpreterParticipants = Array.from(participants.values()).filter(
        (participant) => participant.type === 'interpreter'
    )

    // Reset active participant if it's no longer in the participants map
    useEffect(() => {
        if (activeParticipantId && !participants.has(activeParticipantId)) {
            logger.debug(
                '🔄 Active participant disconnected, clearing selection:',
                activeParticipantId
            )
            setActiveParticipantId(null)
        }
    }, [participants, activeParticipantId])

    const handleParticipantSelect = (participant: ParticipantData) => {
        // If clicking the same participant, deactivate it
        if (activeParticipantId === participant.participantId) {
            participant.unsubscribe()
            setActiveParticipantId(null)
            return
        }

        // Unsubscribe from the currently active participant
        if (activeParticipantId) {
            const currentActive = participants.get(activeParticipantId)
            if (currentActive) {
                currentActive.unsubscribe()
            }
        }

        // Subscribe to the new participant
        participant.subscribe()
        setActiveParticipantId(participant.participantId)
    }

    return (
        <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-800 mb-5 flex items-center gap-2">
                <span>🌍</span>
                Language Selection
            </h3>

            <div className="flex flex-wrap justify-center gap-3">
                {interpreterParticipants.length > 0 ? (
                    interpreterParticipants.map((participant) => (
                        <ParticipantInfo
                            key={participant.participantId}
                            participant={participant}
                            isActive={
                                activeParticipantId ===
                                participant.participantId
                            }
                            onSelect={() =>
                                handleParticipantSelect(participant)
                            }
                        />
                    ))
                ) : (
                    <div className="text-gray-500 italic text-center py-5 px-4 bg-gray-50 rounded-lg">
                        No interpreters available
                    </div>
                )}
            </div>
        </div>
    )
}
