/**
 * RoomBroadcastTestComponent - Comprehensive test component for the new hook architecture
 *
 * This component tests the integration of:
 * - useRoomManager: Room creation and deletion
 * - useSessionManager: Session creation, joining, and leaving
 * - useBroadcast: Broadcasting start/stop with audio sources
 * - useInterpreters: Adding and removing interpreters during broadcast
 *
 * Tests the full flow: Room -> Session -> Broadcast -> Interpreters -> Cleanup
 */

import { useState } from 'react'
import type { AuthenticatedFetch } from '@/hooks/useAuth'
import {
    useRoomManager,
    CreateRoomParams,
    RoomDetails
} from '@/hooks/useRoomManager'
import { useSessionManager, SessionDetails } from '@/hooks/useSessionManager'
import { useBroadcast, AudioSource } from '@/hooks/useBroadcast'
import {
    useInterpreters,
    Interpreter,
    InterpreterStatus
} from '@/hooks/useInterpreters'
import { Card, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { logger } from '@/logger'
import { LiveKitEventsDebugPanel } from './LiveKitEventsDebugPanel'
import { SessionMetricsDisplay } from './SessionMetricsDisplay'

type AppState =
    | 'idle'
    | 'creating-room'
    | 'creating-session'
    | 'joining-session'
    | 'ready-to-broadcast'
    | 'broadcasting'
    | 'stopping-broadcast'
    | 'leaving-session'
    | 'ending-session'
    | 'deleting-room'

export interface RoomBroadcastTestProps {
    authenticatedFetch: AuthenticatedFetch
    userId: string
    userEmail: string
    signOut: () => Promise<string | undefined>
}

export function RoomBroadcastTestComponent({
    authenticatedFetch,
    userId,
    userEmail,
    signOut
}: RoomBroadcastTestProps) {
    const {
        createRoom,
        deleteRoom,
        loading: roomLoading
    } = useRoomManager(authenticatedFetch, userId)

    const {
        createSession,
        joinSession,
        leaveSession,
        endSession,
        loading: sessionLoading
    } = useSessionManager(authenticatedFetch, userId)

    const { broadcastState, startBroadcast, stopBroadcast, broadcastDetails } =
        useBroadcast()

    const {
        addInterpreter,
        removeInterpreter,
        loading: interpretersLoading
    } = useInterpreters(authenticatedFetch)

    const [appState, setAppState] = useState<AppState>('idle')
    const [currentRoom, setCurrentRoom] = useState<RoomDetails | null>(null)
    const [currentSession, setCurrentSession] = useState<SessionDetails | null>(
        null
    )
    const [audioSource, setAudioSource] = useState<AudioSource>('microphone')
    const [interpreters, setInterpreters] = useState<Interpreter[]>([])

    // Form state for room creation
    const [roomForm, setRoomForm] = useState<CreateRoomParams>({
        title: 'Full Lifecycle Test Room',
        description: 'Testing room creation, broadcasting, and deletion.',
        url_identifier: null
    })

    const [interpreterForm, setInterpreterForm] = useState({
        sourceLanguage: 'en-US',
        targetLanguage: 'es-ES'
    })

    const handleCreateRoom = async () => {
        try {
            setAppState('creating-room')
            const roomDetails = await createRoom(roomForm)
            setCurrentRoom(roomDetails)
            setAppState('idle')
            logger.info('Room created successfully:', roomDetails)
        } catch (error) {
            logger.error('Failed to create room:', error)
            setAppState('idle')
        }
    }

    const handleCreateAndJoinSession = async () => {
        logger.debug(
            'handleCreateAndJoinSession called with currentRoom:',
            currentRoom
        )
        logger.debug('currentRoom?.room_id:', currentRoom?.room_id)

        if (!currentRoom?.room_id) {
            logger.error('No room available to create session')
            return
        }

        try {
            setAppState('creating-session')
            const sessionDetails = await createSession({
                room_id: currentRoom.room_id,
                title: null
            })

            setAppState('joining-session')
            const joinedSession = await joinSession(sessionDetails)
            setCurrentSession(joinedSession)
            setAppState('ready-to-broadcast')
            logger.info(
                'Session created and joined successfully:',
                joinedSession
            )
        } catch (error) {
            logger.error('Failed to create/join session:', error)
            setAppState('idle')
        }
    }

    const handleStartBroadcast = async () => {
        if (!currentSession) {
            logger.error('No session available to start broadcast')
            return
        }

        try {
            await startBroadcast(currentSession, audioSource)
            setAppState('broadcasting')
            logger.info('Broadcast started successfully')
        } catch (error) {
            logger.error('Failed to start broadcast:', error)
        }
    }

    const handleStopBroadcast = async () => {
        if (!broadcastDetails) {
            logger.error('No broadcast to stop')
            return
        }

        try {
            setAppState('stopping-broadcast')
            await stopBroadcast()
            setAppState('ready-to-broadcast')
            setInterpreters([]) // Clear interpreters on broadcast stop
            logger.info('Broadcast stopped successfully')
        } catch (error) {
            logger.error('Failed to stop broadcast:', error)
            setAppState('broadcasting') // Revert state on error
        }
    }

    const handleLeaveSession = async () => {
        if (!currentSession) {
            logger.error('No session to leave')
            return
        }

        try {
            setAppState('leaving-session')
            await leaveSession(currentSession)
            setAppState('ending-session')
            await endSession(currentSession)
            setCurrentSession(null)
            setInterpreters([])
            setAppState('idle')
            logger.info('Session left & ended successfully')
        } catch (error) {
            logger.error('Failed to leave/end session:', error)
            setAppState('ready-to-broadcast') // Revert state on error
        }
    }

    const handleDeleteRoom = async () => {
        if (!currentRoom) {
            logger.error('No room to delete')
            return
        }

        try {
            setAppState('deleting-room')
            await deleteRoom(currentRoom)
            setCurrentRoom(null)
            setCurrentSession(null)
            setInterpreters([])
            setAppState('idle')
            logger.info('Room deleted successfully')
        } catch (error) {
            logger.error('Failed to delete room:', error)
            setAppState('idle') // Reset state regardless
        }
    }

    const handleFullCleanup = async () => {
        try {
            // Stop broadcast if active
            if (broadcastDetails && appState === 'broadcasting') {
                await handleStopBroadcast()
            }

            // Leave session if joined
            if (currentSession) {
                await handleLeaveSession()
            }

            // Delete room if created
            if (currentRoom) {
                await handleDeleteRoom()
            }
        } catch (error) {
            logger.error('Error during full cleanup:', error)
        }
    }

    const handleAddInterpreter = async () => {
        if (
            !currentSession?.livekit_room ||
            !currentRoom?.room_id ||
            !currentSession?.livekit_room_id
        ) {
            logger.error('No active session or room to add interpreter')
            return
        }

        try {
            const newInterpreter = await addInterpreter(
                {
                    sessionId: currentSession.session_id,
                    sourceLanguage: interpreterForm.sourceLanguage,
                    targetLanguage: interpreterForm.targetLanguage,
                    livekitRoomId: currentSession?.livekit_room_id
                },
                currentSession.livekit_room,
                // Status change callback
                (interpreterId: string, status: InterpreterStatus) => {
                    setInterpreters((prev) =>
                        prev.map((interpreter) =>
                            interpreter.id === interpreterId
                                ? { ...interpreter, status }
                                : interpreter
                        )
                    )
                }
            )

            // Add the new interpreter to state
            setInterpreters((prev) => [...prev, newInterpreter])
            logger.info('Interpreter added successfully:', newInterpreter)
        } catch (error) {
            logger.error('Failed to add interpreter:', error)
        }
    }

    const handleRemoveInterpreter = async (interpreterId: string) => {
        // Optimistically update status to 'removing'
        setInterpreters((prev) =>
            prev.map((interpreter) =>
                interpreter.id === interpreterId
                    ? { ...interpreter, status: 'removing' }
                    : interpreter
            )
        )

        try {
            await removeInterpreter(interpreterId)

            // Remove from local state after successful API call
            setInterpreters((prev) =>
                prev.filter((i) => i.id !== interpreterId)
            )

            // Clean up event listeners if cleanup function exists
            const interpreter = interpreters.find((i) => i.id === interpreterId)
            if (interpreter && (interpreter as any).cleanup) {
                ;(interpreter as any).cleanup()
            }

            logger.info('Interpreter removed successfully:', interpreterId)
        } catch (error) {
            // Revert status on error
            setInterpreters((prev) =>
                prev.map((interpreter) =>
                    interpreter.id === interpreterId
                        ? { ...interpreter, status: 'pending' }
                        : interpreter
                )
            )
            logger.error('Failed to remove interpreter:', error)
        }
    }

    return (
        <div className="min-h-screen bg-gray-100 p-4">
            <div className="max-w-4xl mx-auto space-y-6">
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <h1 className="text-3xl font-bold">
                                    Room & Broadcast Lifecycle Test (New
                                    Architecture)
                                </h1>
                                <p className="text-gray-600 mt-1">
                                    End-to-end test for the new hook-based
                                    architecture: Room → Session → Broadcast →
                                    Interpreters → Cleanup
                                </p>
                            </div>
                            <div className="text-right">
                                <p className="text-sm text-gray-600">
                                    Signed in as:
                                </p>
                                <p className="font-medium">{userEmail}</p>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={signOut}
                                    className="mt-2"
                                >
                                    Sign Out
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <h2 className="text-xl font-semibold mb-4">
                            Current Status
                        </h2>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div>
                                <p className="text-sm font-medium text-gray-600">
                                    App State
                                </p>
                                <p className="text-lg font-mono">{appState}</p>
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-600">
                                    Broadcast State
                                </p>
                                <p className="text-lg font-mono">
                                    {broadcastState}
                                </p>
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-600">
                                    Room Loading
                                </p>
                                <p className="text-lg font-mono">
                                    {roomLoading ? 'Yes' : 'No'}
                                </p>
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-600">
                                    Session Loading
                                </p>
                                <p className="text-lg font-mono">
                                    {sessionLoading ? 'Yes' : 'No'}
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Session Metrics Display - Only show when there's an active session */}
                {currentSession && (
                    <SessionMetricsDisplay
                        authenticatedFetch={authenticatedFetch}
                        sessionId={currentSession.session_id}
                    />
                )}

                <Card>
                    <CardContent className="p-6">
                        <h2 className="text-xl font-semibold mb-4">
                            Audio Source
                        </h2>
                        <div className="flex gap-4">
                            <label className="flex items-center gap-2">
                                <input
                                    type="radio"
                                    value="microphone"
                                    checked={audioSource === 'microphone'}
                                    onChange={(e) =>
                                        setAudioSource(
                                            e.target.value as AudioSource
                                        )
                                    }
                                    disabled={
                                        appState !== 'idle' &&
                                        appState !== 'ready-to-broadcast'
                                    }
                                />
                                <span>Microphone</span>
                            </label>
                            <label className="flex items-center gap-2">
                                <input
                                    type="radio"
                                    value="browser_audio"
                                    checked={audioSource === 'browser_audio'}
                                    onChange={(e) =>
                                        setAudioSource(
                                            e.target.value as AudioSource
                                        )
                                    }
                                    disabled={
                                        appState !== 'idle' &&
                                        appState !== 'ready-to-broadcast'
                                    }
                                />
                                <span>Browser/Tab Audio</span>
                            </label>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <h2 className="text-xl font-semibold mb-4">
                            Room Configuration
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium mb-1">
                                    Title
                                </label>
                                <input
                                    type="text"
                                    value={roomForm.title}
                                    onChange={(e) =>
                                        setRoomForm((prev) => ({
                                            ...prev,
                                            title: e.target.value
                                        }))
                                    }
                                    className="w-full p-2 border rounded-md"
                                    disabled={appState !== 'idle'}
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium mb-1">
                                    URL Identifier (optional)
                                </label>
                                <input
                                    type="text"
                                    value={roomForm.url_identifier || ''}
                                    onChange={(e) =>
                                        setRoomForm((prev) => ({
                                            ...prev,
                                            url_identifier:
                                                e.target.value || null
                                        }))
                                    }
                                    className="w-full p-2 border rounded-md"
                                    disabled={appState !== 'idle'}
                                    placeholder="Leave empty for auto-generation"
                                />
                            </div>
                            <div className="md:col-span-2">
                                <label className="block text-sm font-medium mb-1">
                                    Description
                                </label>
                                <textarea
                                    value={roomForm.description || ''}
                                    onChange={(e) =>
                                        setRoomForm((prev) => ({
                                            ...prev,
                                            description: e.target.value || null
                                        }))
                                    }
                                    className="w-full p-2 border rounded-md"
                                    rows={3}
                                    disabled={appState !== 'idle'}
                                />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <h2 className="text-xl font-semibold mb-4">Actions</h2>
                        <div className="flex flex-wrap gap-4">
                            <Button
                                onClick={handleCreateRoom}
                                disabled={appState !== 'idle' || roomLoading}
                            >
                                {appState === 'creating-room'
                                    ? 'Creating...'
                                    : '1. Create Room'}
                            </Button>

                            <Button
                                onClick={handleCreateAndJoinSession}
                                disabled={
                                    !currentRoom ||
                                    appState !== 'idle' ||
                                    sessionLoading
                                }
                            >
                                {appState === 'creating-session'
                                    ? 'Creating...'
                                    : appState === 'joining-session'
                                      ? 'Joining...'
                                      : '2. Create & Join Session'}
                            </Button>

                            <Button
                                onClick={handleStartBroadcast}
                                disabled={appState !== 'ready-to-broadcast'}
                            >
                                3. Start Broadcast
                            </Button>

                            <Button
                                onClick={handleStopBroadcast}
                                disabled={appState !== 'broadcasting'}
                                variant="destructive"
                            >
                                4. Stop Broadcast
                            </Button>

                            <Button
                                onClick={handleLeaveSession}
                                disabled={
                                    !currentSession || appState === 'idle'
                                }
                                variant="outline"
                            >
                                5. Leave Session
                            </Button>

                            <Button
                                onClick={handleDeleteRoom}
                                disabled={!currentRoom}
                                variant="outline"
                            >
                                6. Delete Room
                            </Button>

                            <Button
                                onClick={handleFullCleanup}
                                disabled={appState === 'idle'}
                                variant="destructive"
                                className="ml-4"
                            >
                                🧹 Full Cleanup
                            </Button>
                        </div>

                        {/* Current Resource Details */}
                        <div className="mt-4 space-y-2">
                            {currentRoom && (
                                <div className="p-3 bg-blue-50 rounded-md">
                                    <h3 className="font-medium text-blue-900 mb-1">
                                        Current Room:
                                    </h3>
                                    <pre className="text-xs bg-blue-100 p-2 rounded overflow-auto">
                                        {JSON.stringify(currentRoom, null, 2)}
                                    </pre>
                                </div>
                            )}

                            {currentSession && (
                                <div className="p-3 bg-green-50 rounded-md">
                                    <h3 className="font-medium text-green-900 mb-1">
                                        Current Session:
                                    </h3>
                                    <pre className="text-xs bg-green-100 p-2 rounded overflow-auto">
                                        {JSON.stringify(
                                            {
                                                session_id:
                                                    currentSession.session_id,
                                                livekit_room_id:
                                                    currentSession.livekit_room_id,
                                                hasLkRoom:
                                                    !!currentSession.livekit_room
                                            },
                                            null,
                                            2
                                        )}
                                    </pre>
                                </div>
                            )}

                            {broadcastDetails && (
                                <div className="p-3 bg-purple-50 rounded-md">
                                    <h3 className="font-medium text-purple-900 mb-1">
                                        Current Broadcast:
                                    </h3>
                                    <pre className="text-xs bg-purple-100 p-2 rounded overflow-auto">
                                        {JSON.stringify(
                                            {
                                                hasAudioTrack:
                                                    !!broadcastDetails._audioTrack,
                                                broadcastState
                                            },
                                            null,
                                            2
                                        )}
                                    </pre>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {appState === 'broadcasting' && (
                    <Card>
                        <CardContent className="p-6">
                            <h2 className="text-xl font-semibold mb-4">
                                Interpreters
                            </h2>
                            <div className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium mb-1">
                                            Source Language
                                        </label>
                                        <input
                                            type="text"
                                            value={
                                                interpreterForm.sourceLanguage
                                            }
                                            onChange={(e) =>
                                                setInterpreterForm((prev) => ({
                                                    ...prev,
                                                    sourceLanguage:
                                                        e.target.value
                                                }))
                                            }
                                            className="w-full p-2 border rounded-md"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium mb-1">
                                            Target Language
                                        </label>
                                        <input
                                            type="text"
                                            value={
                                                interpreterForm.targetLanguage
                                            }
                                            onChange={(e) =>
                                                setInterpreterForm((prev) => ({
                                                    ...prev,
                                                    targetLanguage:
                                                        e.target.value
                                                }))
                                            }
                                            className="w-full p-2 border rounded-md"
                                        />
                                    </div>
                                    <div className="self-end">
                                        <Button
                                            onClick={handleAddInterpreter}
                                            className="w-full"
                                            disabled={interpretersLoading}
                                        >
                                            {interpretersLoading
                                                ? 'Adding...'
                                                : 'Add Interpreter'}
                                        </Button>
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <h3 className="text-lg font-medium">
                                        Current Interpreters (
                                        {interpreters.length})
                                    </h3>
                                    {interpreters.length === 0 ? (
                                        <p className="text-gray-500">
                                            No interpreters added.
                                        </p>
                                    ) : (
                                        <ul className="divide-y divide-gray-200">
                                            {interpreters.map((interpreter) => (
                                                <li
                                                    key={interpreter.id}
                                                    className="py-2 flex items-center justify-between"
                                                >
                                                    <div>
                                                        <p className="font-mono text-sm">
                                                            {interpreter.id}
                                                        </p>
                                                        <p className="text-xs text-gray-600">
                                                            {
                                                                interpreter.sourceLanguage
                                                            }{' '}
                                                            →{' '}
                                                            {
                                                                interpreter.targetLanguage
                                                            }
                                                        </p>
                                                    </div>
                                                    <div className="flex items-center gap-4">
                                                        <span
                                                            className={`text-sm font-medium capitalize px-2 py-1 rounded-full ${
                                                                interpreter.status ===
                                                                'active'
                                                                    ? 'bg-green-100 text-green-800'
                                                                    : interpreter.status ===
                                                                        'removing'
                                                                      ? 'bg-red-100 text-red-800'
                                                                      : 'bg-blue-100 text-blue-800'
                                                            }`}
                                                        >
                                                            {interpreter.status}
                                                        </span>
                                                        <Button
                                                            variant="destructive"
                                                            size="sm"
                                                            onClick={() =>
                                                                handleRemoveInterpreter(
                                                                    interpreter.id
                                                                )
                                                            }
                                                            disabled={
                                                                interpretersLoading ||
                                                                interpreter.status ===
                                                                    'removing'
                                                            }
                                                        >
                                                            Remove
                                                        </Button>
                                                    </div>
                                                </li>
                                            ))}
                                        </ul>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* LiveKit Events Debug Panel */}
                <LiveKitEventsDebugPanel
                    room={currentSession?.livekit_room || null}
                    title="LiveKit Room Events Debug"
                    maxEvents={50}
                    collapsed={appState === 'idle'}
                />
            </div>
        </div>
    )
}
