import React from 'react'
import { createRoot } from 'react-dom/client'
import { ListenerApp } from './components/ListenerApp'
import ErrorBoundary from './components/ErrorBoundary'
import './globals.css'

const container = document.getElementById('root')
if (!container) {
    throw new Error('Root element not found')
}

const root = createRoot(container)
root.render(
    <React.StrictMode>
        <ErrorBoundary>
            <ListenerApp />
        </ErrorBoundary>
    </React.StrictMode>
)
