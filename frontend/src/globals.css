@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 222.2 84% 4.9%;
        --card: 0 0% 100%;
        --card-foreground: 222.2 84% 4.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 222.2 84% 4.9%;
        --primary: 221.2 83.2% 53.3%;
        --primary-foreground: 210 40% 98%;
        --secondary: 210 40% 96%;
        --secondary-foreground: 222.2 84% 4.9%;
        --muted: 210 40% 96%;
        --muted-foreground: 215.4 16.3% 46.9%;
        --accent: 210 40% 96%;
        --accent-foreground: 222.2 84% 4.9%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 210 40% 98%;
        --success: 142 76% 36%;
        --success-foreground: 0 0% 100%;
        --warning: 38 92% 50%;
        --warning-foreground: 0 0% 100%;
        --border: 214.3 31.8% 91.4%;
        --input: 214.3 31.8% 91.4%;
        --ring: 221.2 83.2% 53.3%;
        --radius: 0.75rem;

        /* Gradients for the auth component styling */
        --gradient-primary: linear-gradient(
            135deg,
            hsl(263 85% 65%),
            hsl(224 100% 70%)
        );
        --gradient-background: linear-gradient(
            135deg,
            hsl(263 85% 65%),
            hsl(252 100% 85%)
        );
        --gradient-card: linear-gradient(
            145deg,
            hsla(0 0% 100% / 0.9),
            hsla(252 100% 97% / 0.8)
        );

        /* Shadows for enhanced visual appeal */
        --shadow-broadcast: 0 20px 40px -10px hsla(263 85% 65% / 0.2);
        --shadow-card: 0 10px 30px -5px hsla(263 85% 65% / 0.1);
        --shadow-button: 0 5px 15px -3px hsla(263 85% 65% / 0.3);

        /* Animation timing functions */
        --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);

        /* Auth-specific colors (matching ai-live-studio theme) */
        --auth-primary: 263 85% 65%;
        --auth-primary-foreground: 0 0% 100%;

        /* Dashboard-specific colors (matching ai-live-studio purple theme) */
        --dashboard-primary: 263 85% 65%;
        --dashboard-primary-foreground: 0 0% 100%;
    }

    .dark {
        --background: 222.2 84% 4.9%;
        --foreground: 210 40% 98%;
        --card: 222.2 84% 4.9%;
        --card-foreground: 210 40% 98%;
        --popover: 222.2 84% 4.9%;
        --popover-foreground: 210 40% 98%;
        --primary: 210 40% 98%;
        --primary-foreground: 222.2 84% 4.9%;
        --secondary: 217.2 32.6% 17.5%;
        --secondary-foreground: 210 40% 98%;
        --muted: 217.2 32.6% 17.5%;
        --muted-foreground: 215 20.2% 65.1%;
        --accent: 217.2 32.6% 17.5%;
        --accent-foreground: 210 40% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 210 40% 98%;
        --success: 142 76% 36%;
        --success-foreground: 0 0% 100%;
        --warning: 38 92% 50%;
        --warning-foreground: 0 0% 100%;
        --border: 217.2 32.6% 17.5%;
        --input: 217.2 32.6% 17.5%;
        --ring: 212.7 26.8% 83.9%;

        /* Dark mode gradients for auth component */
        --gradient-primary: linear-gradient(
            135deg,
            hsl(263 85% 65%),
            hsl(224 100% 70%)
        );
        --gradient-background: linear-gradient(
            135deg,
            hsl(221 39% 8%),
            hsl(263 85% 15%)
        );
        --gradient-card: linear-gradient(
            145deg,
            hsla(221 39% 12% / 0.9),
            hsla(263 85% 10% / 0.8)
        );

        /* Auth-specific colors (same for dark mode) */
        --auth-primary: 263 85% 65%;
        --auth-primary-foreground: 0 0% 100%;

        /* Dashboard-specific colors (same for dark mode) */
        --dashboard-primary: 263 85% 65%;
        --dashboard-primary-foreground: 0 0% 100%;
    }
}

@layer base {
    * {
        @apply border-border;
    }
    body {
        @apply bg-background text-foreground;
    }
}
