import { RemoteParticipant, RemoteAudioTrack } from 'livekit-client'

export interface ParticipantData {
    participantId: string
    participant: RemoteParticipant
    type: 'interpreter' | 'broadcaster'
    language: string | null
    displayName: string
    flagEmoji: string
    subscribe: () => void
    unsubscribe: () => void
}

export interface AudioTrack {
    track: RemoteAudioTrack
    element: HTMLAudioElement
    participant: ParticipantData
}

export type ParticipantType = 'interpreter' | 'broadcaster'

export interface AudioPermissionEventDetail {
    callback: () => void
}

export interface LiveKitEventHandlers {
    onRoomInfo?: (title: string, description: string) => void
    onTrackPublished?: (
        participant: RemoteParticipant,
        subscribe: () => void,
        unsubscribe: () => void
    ) => void
    onTrackSubscribed?: (
        participant: RemoteParticipant,
        track: RemoteAudioTrack
    ) => void
    onTrackUnsubscribed?: (
        participant: RemoteParticipant,
        track: RemoteAudioTrack
    ) => void
    onParticipantDisconnected?: (id: string) => void
}
