import { useState, useEffect, useCallback, useRef } from 'react'
import {
    Room,
    RoomEvent,
    RemoteTrackPublication,
    RemoteParticipant,
    RemoteAudioTrack,
    RemoteTrack,
    DisconnectReason
} from 'livekit-client'
import { logger } from '@/logger'
import { ParticipantData } from '@/types/reactTypes'
import { useHeartbeat } from '@/hooks/useHeartbeat'

interface TokenData {
    token: string
    ws_url: string
}

interface UseLiveKitOptions {
    autoSubscribe?: boolean
    heartbeatInterval?: number
}

// Event handler types
type TrackPublishedHandler = (
    participant: RemoteParticipant,
    subscribe: () => void,
    unsubscribe: () => void
) => void
type TrackSubscribedHandler = (
    participant: RemoteParticipant,
    track: RemoteAudioTrack
) => void
type TrackUnsubscribedHandler = (
    participant: RemoteParticipant,
    track: RemoteAudioTrack
) => void
type ParticipantDisconnectedHandler = (id: string) => void

type EventHandlerMap = {
    onTrackPublished: TrackPublishedHandler
    onTrackSubscribed: TrackSubscribedHandler
    onTrackUnsubscribed: TrackUnsubscribedHandler
    onParticipantDisconnected: ParticipantDisconnectedHandler
}

type EventType = keyof EventHandlerMap

interface UseLiveKitReturn {
    room: Room | null
    participants: Map<string, ParticipantData>
    connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'error'
    error: string | null
    connect: () => Promise<void>
    disconnect: () => void
    subscribeToParticipant: (participantId: string) => void
    unsubscribeFromParticipant: (participantId: string) => void
    registerEventHandler: <T extends EventType>(
        event: T,
        handler: EventHandlerMap[T]
    ) => () => void
}

export function useLiveKit(
    livekitRoomId: string | null,
    activeLanguage: string | null,
    options: UseLiveKitOptions = {}
): UseLiveKitReturn {
    const { autoSubscribe = false, heartbeatInterval = 60000 } = options

    // State
    const [room, setRoom] = useState<Room | null>(null)
    const [participants, setParticipants] = useState<
        Map<string, ParticipantData>
    >(new Map())
    const [connectionStatus, setConnectionStatus] = useState<
        'disconnected' | 'connecting' | 'connected' | 'error'
    >('disconnected')
    const [error, setError] = useState<string | null>(null)

    // Refs
    const roomRef = useRef<Room | null>(null)
    const tokenRef = useRef<string | null>(null)

    // Heartbeat hook
    const { startHeartbeat, stopHeartbeat, updateListenerData } = useHeartbeat(
        {
            livekitRoomId: livekitRoomId,
            durationSecs: heartbeatInterval / 1000,
            // TODO: Hardcoded source language
            sourceLanguage: 'en',
            targetLanguage: activeLanguage
        },
        {
            interval: heartbeatInterval,
            enabled: true
        }
    )

    // Keep heartbeat data in sync with the props
    useEffect(() => {
        updateListenerData({
            livekitRoomId: livekitRoomId,
            sourceLanguage: 'en',
            targetLanguage: activeLanguage,
            durationSecs: heartbeatInterval / 1000
        })
    }, [livekitRoomId, activeLanguage, heartbeatInterval, updateListenerData])

    // Event handlers that will be passed to components
    const [eventHandlers] = useState(() => ({
        onTrackPublished: new Set<TrackPublishedHandler>(),
        onTrackSubscribed: new Set<TrackSubscribedHandler>(),
        onTrackUnsubscribed: new Set<TrackUnsubscribedHandler>(),
        onParticipantDisconnected: new Set<ParticipantDisconnectedHandler>()
    }))

    // Utility functions
    const getErrorMessage = useCallback(
        (
            status: number,
            defaultMessage = 'Unable to connect to the live stream.'
        ): string => {
            const errorMessages: Record<number, string> = {
                404: 'This live stream room is not available. Please check the room link.',
                410: 'This live stream session has expired. The event has ended.',
                503: 'This live stream is currently full. Please try again later.',
                401: 'Your session has expired. Please refresh the page.',
                403: 'Access denied. Please refresh the page.'
            }
            return errorMessages[status] || defaultMessage
        },
        []
    )

    const isTranslatorTrack = useCallback(
        (participantIdentity: string): boolean => {
            return /^translator-bot_[a-zA-Z-]+$/.test(participantIdentity)
        },
        []
    )

    const extractLanguageFromTranslator = useCallback(
        (participantIdentity: string): string | null => {
            const match = participantIdentity.match(
                /^translator-bot_([a-zA-Z-]+)$/
            )
            return match ? match[1] : null
        },
        []
    )

    const getLanguageDisplayName = useCallback(
        (languageCode: string | null): string => {
            if (!languageCode) return 'Original Audio'

            // Basic language mapping - can be expanded with language-constants
            const languageMap: Record<string, string> = {
                en: 'English',
                es: 'Spanish',
                fr: 'French',
                de: 'German',
                it: 'Italian',
                pt: 'Portuguese',
                ru: 'Russian',
                ja: 'Japanese',
                ko: 'Korean',
                zh: 'Chinese',
                ar: 'Arabic',
                hi: 'Hindi'
            }

            const baseCode = languageCode.slice(0, 2)
            return languageMap[baseCode] || languageCode.toUpperCase()
        },
        []
    )

    const getLanguageFlag = useCallback(
        (languageCode: string | null): string => {
            if (!languageCode) return '🎙️'

            const flagMap: Record<string, string> = {
                en: '🇺🇸',
                es: '🇪🇸',
                fr: '🇫🇷',
                de: '🇩🇪',
                it: '🇮🇹',
                pt: '🇵🇹',
                ru: '🇷🇺',
                ja: '🇯🇵',
                ko: '🇰🇷',
                zh: '🇨🇳',
                ar: '🇸🇦',
                hi: '🇮🇳'
            }

            const baseCode = languageCode.slice(0, 2)
            return flagMap[baseCode] || '🏳️'
        },
        []
    )

    // Participant management
    const addParticipant = useCallback(
        (
            livekitParticipant: RemoteParticipant,
            subscribe: () => void,
            unsubscribe: () => void
        ): ParticipantData => {
            const participantId = livekitParticipant.identity
            const isInterpreter = isTranslatorTrack(participantId)
            const language = isInterpreter
                ? extractLanguageFromTranslator(participantId)
                : null

            const participantData: ParticipantData = {
                participantId,
                participant: livekitParticipant,
                type: isInterpreter ? 'interpreter' : 'broadcaster',
                language,
                displayName: getLanguageDisplayName(language),
                flagEmoji: getLanguageFlag(language),
                subscribe,
                unsubscribe
            }

            setParticipants(
                (prev) => new Map(prev.set(participantId, participantData))
            )

            logger.debug(
                `👤 LIVEKIT HOOK: Added ${participantData.type} participant:`,
                {
                    identity: participantId,
                    language: participantData.language
                }
            )

            return participantData
        },
        [
            isTranslatorTrack,
            extractLanguageFromTranslator,
            getLanguageDisplayName,
            getLanguageFlag
        ]
    )

    const removeParticipant = useCallback(
        (participantId: string): ParticipantData | null => {
            let removedParticipant: ParticipantData | null = null

            setParticipants((prev) => {
                removedParticipant = prev.get(participantId) || null
                if (!removedParticipant) {
                    logger.warn(
                        '⚠️ LIVEKIT HOOK: Participant not found:',
                        participantId
                    )
                    return prev
                }

                const newMap = new Map(prev)
                newMap.delete(participantId)
                logger.debug(
                    `🗑️ LIVEKIT HOOK: Removed ${removedParticipant.type} participant:`,
                    participantId
                )
                return newMap
            })

            return removedParticipant
        },
        []
    )

    // Track subscription management
    const handleParticipantTrackSubscriptions = useCallback(
        (
            participant: RemoteParticipant,
            publication: RemoteTrackPublication
        ) => {
            if (publication instanceof RemoteTrackPublication) {
                const subscribeCallback = () => {
                    publication.setSubscribed(true)
                }

                const unsubscribeCallback = () => {
                    publication.setSubscribed(false)
                }

                // Add participant to our state
                addParticipant(
                    participant,
                    subscribeCallback,
                    unsubscribeCallback
                )

                // Notify event handlers
                eventHandlers.onTrackPublished.forEach((handler) => {
                    handler(participant, subscribeCallback, unsubscribeCallback)
                })

                // Auto-subscribe to broadcaster tracks
                if (participant.identity === 'broadcaster') {
                    publication.setSubscribed(true)
                }
            }
        },
        [addParticipant, eventHandlers]
    )

    // Room event setup
    const setupRoomEventListeners = useCallback(
        (room: Room) => {
            // Participant connected
            room.on(
                RoomEvent.ParticipantConnected,
                (participant: RemoteParticipant) => {
                    logger.debug(
                        '🎭 LIVEKIT HOOK: A new participant connected!',
                        participant.identity
                    )
                    for (const publication of participant.trackPublications.values()) {
                        handleParticipantTrackSubscriptions(
                            participant,
                            publication as RemoteTrackPublication
                        )
                    }
                }
            )

            // Participant disconnected
            room.on(
                RoomEvent.ParticipantDisconnected,
                (participant: RemoteParticipant) => {
                    logger.debug(
                        '🎭 LIVEKIT HOOK: A participant disconnected!',
                        participant.identity
                    )

                    // Remove participant from state (this will trigger UI update)
                    removeParticipant(participant.identity)

                    // Notify event handlers for additional cleanup
                    eventHandlers.onParticipantDisconnected.forEach(
                        (handler) => {
                            handler(participant.identity)
                        }
                    )
                }
            )

            // Track published
            room.on(
                RoomEvent.TrackPublished,
                (
                    publication: RemoteTrackPublication,
                    participant: RemoteParticipant
                ) => {
                    logger.debug(
                        '📡 LIVEKIT HOOK: A track published from',
                        participant.identity
                    )
                    if (publication.kind === 'audio') {
                        handleParticipantTrackSubscriptions(
                            participant,
                            publication
                        )
                    }
                }
            )

            // Track subscribed
            room.on(
                RoomEvent.TrackSubscribed,
                (
                    track: RemoteTrack,
                    _publication: RemoteTrackPublication,
                    participant: RemoteParticipant
                ) => {
                    if (track.kind === 'audio') {
                        logger.debug('🎵 LIVEKIT HOOK: Track subscribed:', {
                            participant: participant.identity,
                            trackKind: track.kind
                        })

                        // Notify event handlers
                        eventHandlers.onTrackSubscribed.forEach((handler) => {
                            handler(participant, track as RemoteAudioTrack)
                        })
                    }
                }
            )

            // Track unsubscribed
            room.on(
                RoomEvent.TrackUnsubscribed,
                (
                    track: RemoteTrack,
                    publication: RemoteTrackPublication,
                    participant: RemoteParticipant
                ) => {
                    if (track.kind === 'audio') {
                        logger.debug('🔇 LIVEKIT HOOK: Track unsubscribed:', {
                            participant: participant.identity,
                            trackKind: track.kind,
                            trackSid: publication.trackSid
                        })

                        // Notify event handlers
                        eventHandlers.onTrackUnsubscribed.forEach((handler) => {
                            handler(participant, track as RemoteAudioTrack)
                        })
                    }
                }
            )

            // Connection quality changed
            room.on(
                RoomEvent.ConnectionQualityChanged,
                (quality, participant) => {
                    logger.debug(
                        '📶 LIVEKIT HOOK: Connection quality changed:',
                        {
                            participant: participant?.identity || 'local',
                            quality
                        }
                    )
                }
            )

            // Disconnected
            room.on(RoomEvent.Disconnected, (reason?: DisconnectReason) => {
                logger.debug('💔 LIVEKIT HOOK: Room disconnected:', reason)
                setConnectionStatus('disconnected')
                if (reason) {
                    setError(`Disconnected: ${reason}`)
                }
            })
        },
        [handleParticipantTrackSubscriptions, removeParticipant, eventHandlers]
    )

    // Main connection function
    const connect = useCallback(async () => {
        if (!livekitRoomId) {
            setError('No livekit room ID provided')
            return
        }

        try {
            setConnectionStatus('connecting')
            setError(null)

            logger.info('🚀 LIVEKIT HOOK: Connecting to live stream...')

            // Get token from API
            const tokenUrl = `/api/listener/get-token/${encodeURIComponent(livekitRoomId)}`
            const tokenResponse = await fetch(tokenUrl, {
                method: 'POST',
                credentials: 'include'
            })

            if (!tokenResponse.ok) {
                const errorMessage = getErrorMessage(tokenResponse.status)
                throw new Error(errorMessage)
            }

            const tokenData: TokenData = await tokenResponse.json()
            tokenRef.current = tokenData.token

            // Create and connect to room
            const newRoom = new Room()
            roomRef.current = newRoom
            setRoom(newRoom)

            setupRoomEventListeners(newRoom)

            await newRoom.connect(tokenData.ws_url, tokenData.token, {
                autoSubscribe
            })

            // Process participants already in the room
            for (const participant of newRoom.remoteParticipants.values()) {
                logger.debug(
                    '🎭 LIVEKIT HOOK: Participant already in room:',
                    participant.identity
                )
                for (const publication of participant.trackPublications.values()) {
                    handleParticipantTrackSubscriptions(
                        participant,
                        publication as RemoteTrackPublication
                    )
                }
            }

            setConnectionStatus('connected')
            logger.info('✅ LIVEKIT HOOK: Connected to live stream!')

            startHeartbeat()
        } catch (err) {
            const errorMessage =
                err instanceof Error
                    ? err.message
                    : 'Failed to connect to live stream'
            logger.error('❌ LIVEKIT HOOK: Connection failed:', errorMessage)
            setError(errorMessage)
            setConnectionStatus('error')
        }
    }, [
        livekitRoomId,
        getErrorMessage,
        autoSubscribe,
        setupRoomEventListeners,
        handleParticipantTrackSubscriptions,
        startHeartbeat
    ])

    // Disconnect function
    const disconnect = useCallback(async () => {
        logger.info('🔌 LIVEKIT HOOK: Disconnecting...')

        stopHeartbeat()

        if (roomRef.current) {
            await roomRef.current.disconnect()
            roomRef.current = null
        }

        setRoom(null)
        setParticipants(new Map())
        setConnectionStatus('disconnected')
        setError(null)
    }, [stopHeartbeat])

    // Subscription management functions
    const subscribeToParticipant = useCallback(
        (participantId: string) => {
            const participant = participants.get(participantId)
            if (participant) {
                participant.subscribe()
                logger.debug(
                    '🔔 LIVEKIT HOOK: Subscribed to participant:',
                    participantId
                )
            }
        },
        [participants]
    )

    const unsubscribeFromParticipant = useCallback(
        (participantId: string) => {
            const participant = participants.get(participantId)
            if (participant) {
                participant.unsubscribe()
                logger.debug(
                    '🔕 LIVEKIT HOOK: Unsubscribed from participant:',
                    participantId
                )
            }
        },
        [participants]
    )

    // Auto-connect when roomId changes
    useEffect(() => {
        if (livekitRoomId && connectionStatus === 'disconnected') {
            connect()
        }
    }, [livekitRoomId, connectionStatus, connect])

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            disconnect()
        }
    }, [disconnect])

    // Expose event handler registration (for components that need to listen to events)
    const registerEventHandler = useCallback(
        <T extends EventType>(event: T, handler: EventHandlerMap[T]) => {
            eventHandlers[event].add(handler as any)
            return () => {
                eventHandlers[event].delete(handler as any)
            }
        },
        [eventHandlers]
    )

    return {
        // State
        room,
        participants,
        connectionStatus,
        error,

        // API
        connect,
        disconnect,
        subscribeToParticipant,
        unsubscribeFromParticipant,
        registerEventHandler
    }
}
