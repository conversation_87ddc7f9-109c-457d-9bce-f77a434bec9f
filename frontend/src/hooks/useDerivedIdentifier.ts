import { useCallback, useEffect, useState } from 'react'

// Convert arbitrary text to a URL-safe slug
function slugify(input: string): string {
    return input
        .toLowerCase()
        .normalize('NFKD') // Unicode normalization to decomposed form (e.g., 'é' -> 'e' + accent)
        .replace(/[\u0300-\u036f]/g, '') // remove diacritics
        .replace(/[^a-z0-9-_]+/g, '-') // allow a-z, 0-9, dash and underscore
        .replace(/-{2,}/g, '-') // collapse multiple dashes
        .replace(/^[-_]+|[-_]+$/g, '') // trim leading/trailing dashes/underscores
}

interface UseDerivedIdentifierParams {
    title: string
    setIdentifier: (identifier: string) => void
}

export function useDerivedIdentifier({
    title,
    setIdentifier
}: UseDerivedIdentifierParams) {
    const [isManuallyEdited, setIsManuallyEdited] = useState(false)

    // Memoize setIdentifier to prevent unnecessary re-renders
    const setIdentifierCallback = useCallback(setIdentifier, [setIdentifier])

    // Auto-derive URL identifier from title until manually edited
    useEffect(() => {
        if (isManuallyEdited) return

        const titleSlug = slugify(title).slice(0, 50)
        setIdentifierCallback(titleSlug)
    }, [title, isManuallyEdited, setIdentifierCallback])

    const onManualEdit = useCallback(
        (identifier: string) => {
            setIsManuallyEdited(true)
            setIdentifierCallback(identifier)
        },
        [setIdentifierCallback]
    )

    const resetManualEdit = useCallback(() => {
        setIsManuallyEdited(false)
    }, [])

    return { isManuallyEdited, onManualEdit, resetManualEdit }
}
