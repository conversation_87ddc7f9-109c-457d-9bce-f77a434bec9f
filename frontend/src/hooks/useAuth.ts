/**
 * Authentication Hook - useAuth
 *
 * Example usage:
 * ```tsx
 * function MyComponent() {
 *   const { authState, user, signIn, signOut, loading, error } = useAuth()
 *
 *   if (authState === 'initializing') return <div>Loading...</div>
 *   if (authState === 'unauthenticated') {
 *     return <LoginForm onSignIn={(email, password) => signIn(email, password)} />
 *   }
 *
 *   return (
 *     <div>
 *       Welcome {user?.email}!
 *       <button onClick={signOut}>Logout</button>
 *     </div>
 *   )
 * }
 * ```
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { createClient, SupabaseClient, Session } from '@supabase/supabase-js'
import { logger } from '@/logger'

export type AuthenticatedFetch = {
    (
        url: string,
        options?: RequestInit
    ): Promise<{
        success: boolean
        response?: Response
        error?: string
    }>
}

// Types
export type AuthState = 'initializing' | 'authenticated' | 'unauthenticated'

interface User {
    email: string
    token: string
    [key: string]: any
}

interface UseAuthReturn {
    authState: AuthState
    user: User | null
    loading: boolean
    signIn: (email: string, password: string) => Promise<string | undefined>
    signInWithGoogle: (redirectPath?: string) => Promise<string | undefined>
    signUp: (
        email: string,
        password: string,
        confirmPassword: string,
        redirectPath?: string
    ) => Promise<string | undefined>
    signOut: () => Promise<string | undefined>
    authenticatedFetch: (
        url: string,
        options?: RequestInit
    ) => Promise<{
        success: boolean
        response?: Response
        error?: string
    }>
}

// Environment variables
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL as string
const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY as string

// Validate environment variables
if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
    logger.error(
        'Missing Supabase environment variables. Check your .env file.'
    )
}

export function useAuth(): UseAuthReturn {
    // State
    const [authState, setAuthState] = useState<AuthState>('initializing')
    const [user, setUser] = useState<User | null>(null)
    const [loading, setLoading] = useState(false)

    // Refs
    const supabaseRef = useRef<SupabaseClient | null>(null)
    const initializationPromiseRef = useRef<Promise<void> | null>(null)

    // Initialize Supabase client
    const initializeSupabase = useCallback(async (): Promise<void> => {
        // Return existing promise if already initializing
        if (initializationPromiseRef.current) {
            return initializationPromiseRef.current
        }

        const initPromise = (async () => {
            try {
                const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)
                supabaseRef.current = supabase

                // Set up auth state change listener
                supabase.auth.onAuthStateChange((event, session) => {
                    handleAuthStateChange(event, session)
                })

                // Check for existing session
                const {
                    data: { session },
                    error
                } = await supabase.auth.getSession()

                if (error) {
                    logger.error('Error getting session:', error)
                    setAuthState('unauthenticated')
                    return
                }

                if (session?.access_token && session.user?.email) {
                    setUser({
                        email: session.user.email,
                        token: session.access_token,
                        ...session.user
                    })
                    setAuthState('authenticated')
                } else {
                    setAuthState('unauthenticated')
                }

                logger.debug('Supabase initialized successfully')
            } catch (err: any) {
                logger.error('Failed to initialize Supabase:', err)
                setAuthState('unauthenticated')
            }
        })()

        initializationPromiseRef.current = initPromise
        return initPromise
    }, [])

    // Handle auth state changes from Supabase
    const handleAuthStateChange = useCallback(
        (event: string, session: Session | null) => {
            if (
                event === 'SIGNED_IN' &&
                session?.access_token &&
                session.user?.email
            ) {
                setUser({
                    email: session.user.email,
                    token: session.access_token,
                    ...session.user
                })
                setAuthState('authenticated')
                logger.debug('User signed in:', session.user.email)
            } else if (event === 'SIGNED_OUT') {
                setUser(null)
                setAuthState('unauthenticated')
                logger.debug('User signed out')
            }
        },
        []
    )

    // Validate input
    const validateInput = useCallback(
        (
            email: string,
            password: string,
            confirmPassword?: string
        ): string | null => {
            if (!email.trim()) return 'Email is required'
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email))
                return 'Please enter a valid email address'
            if (!password) return 'Password is required'
            if (password.length < 6)
                return 'Password must be at least 6 characters long'
            if (confirmPassword !== undefined && password !== confirmPassword)
                return 'Passwords do not match'
            return null
        },
        []
    )

    // Sign in
    const signIn = useCallback(
        async (
            email: string,
            password: string
        ): Promise<string | undefined> => {
            const validationError = validateInput(email, password)
            if (validationError) {
                return validationError
            }

            setLoading(true)

            try {
                const supabase = supabaseRef.current
                if (!supabase) {
                    return 'Authentication not initialized'
                }

                const { error } = await supabase.auth.signInWithPassword({
                    email: email.trim(),
                    password
                })

                if (error) {
                    logger.error('Sign in error:', error.message)
                    return error.message
                }
            } catch (err: any) {
                const errorMessage = err.message || 'Failed to sign in'
                logger.error('Sign in error:', err)
                return errorMessage
            } finally {
                setLoading(false)
            }
        },
        [validateInput]
    )

    // Sign up
    const signUp = useCallback(
        async (
            email: string,
            password: string,
            confirmPassword: string,
            redirectPath: string = '/'
        ): Promise<string | undefined> => {
            const validationError = validateInput(
                email,
                password,
                confirmPassword
            )
            if (validationError) {
                return validationError
            }

            setLoading(true)

            try {
                const supabase = supabaseRef.current
                if (!supabase) {
                    return 'Authentication not initialized'
                }

                const redirectUrl = `${window.location.origin}${redirectPath}`

                const { error } = await supabase.auth.signUp({
                    email: email.trim(),
                    password,
                    options: {
                        emailRedirectTo: redirectUrl
                    }
                })

                if (error) {
                    // Specific error handling matching ai-live-studio
                    const errorMessage = error.message.includes(
                        'User already registered'
                    )
                        ? 'An account with this email already exists. Please sign in instead.'
                        : error.message

                    logger.error('Sign up error:', error.message)
                    return errorMessage
                }
            } catch (err: any) {
                const errorMessage =
                    err.message ||
                    'An unexpected error occurred. Please try again.'
                logger.error('Sign up error:', err)
                return errorMessage
            } finally {
                setLoading(false)
            }
        },
        [validateInput]
    )

    // Sign in with Google
    const signInWithGoogle = useCallback(
        async (redirectPath: string = '/'): Promise<string | undefined> => {
            setLoading(true)

            try {
                const supabase = supabaseRef.current
                if (!supabase) {
                    return 'Authentication not initialized'
                }

                const { error } = await supabase.auth.signInWithOAuth({
                    provider: 'google',
                    options: {
                        redirectTo: `${window.location.origin}${redirectPath}`
                    }
                })

                if (error) {
                    logger.error('Google sign in error:', error.message)
                    return error.message
                }
            } catch (err: any) {
                const errorMessage =
                    err.message || 'Failed to sign in with Google'
                logger.error('Google sign in error:', err)
                return errorMessage
            } finally {
                setLoading(false)
            }
        },
        []
    )

    // Sign out
    const signOut = useCallback(async (): Promise<string | undefined> => {
        try {
            const supabase = supabaseRef.current
            if (!supabase) {
                logger.warn('Cannot sign out: authentication not initialized')
                return 'Authentication not initialized'
            }

            const { error } = await supabase.auth.signOut()
            if (error) {
                logger.error('Sign out error:', error.message)
                return error.message
            }
        } catch (err: any) {
            logger.error('Sign out error:', err)
            return err.message || 'Failed to sign out'
        }
    }, [])

    // Authenticated fetch wrapper
    const authenticatedFetch = useCallback(
        async (url: string, options: RequestInit = {}) => {
            if (!user?.token) {
                return {
                    success: false,
                    error: 'User not authenticated'
                }
            }

            const headers: HeadersInit = {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${user.token}`,
                ...options.headers
            }

            try {
                const response = await fetch(url, {
                    ...options,
                    headers
                })

                // Handle 401 unauthorized - sign out user
                if (response.status === 401) {
                    logger.warn('Authentication expired, signing out user')
                    await signOut()
                    return {
                        success: false,
                        error: 'Authentication expired. Please log in again.'
                    }
                }

                return {
                    success: true,
                    response
                }
            } catch (err: any) {
                const errorMessage = `Request failed: ${err.message}`
                logger.error('Authenticated fetch error:', err)
                return {
                    success: false,
                    error: errorMessage
                }
            }
        },
        [user?.token, signOut]
    )

    // Initialize on mount
    useEffect(() => {
        initializeSupabase()
    }, [initializeSupabase])

    return {
        authState,
        user,
        loading,
        signIn,
        signInWithGoogle,
        signUp,
        signOut,
        authenticatedFetch
    }
}
