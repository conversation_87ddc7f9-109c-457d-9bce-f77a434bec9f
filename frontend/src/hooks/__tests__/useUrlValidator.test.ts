import { renderHook, act } from '@testing-library/react'
import { useUrlValidator } from '../useUrlValidator'
import { supabase } from '@/lib/supabase'
import { logger } from '@/logger'
import { vi } from 'vitest'

// Mock dependencies
vi.mock('@/lib/supabase', () => ({
    supabase: {
        from: vi.fn()
    }
}))

vi.mock('@/logger', () => ({
    logger: {
        error: vi.fn()
    }
}))

const mockSupabase = supabase as any

describe('useUrlValidator', () => {
    beforeEach(() => {
        vi.clearAllMocks()
    })

    describe('validateUrlIdentifier', () => {
        it('should return true for valid identifiers', () => {
            const { result } = renderHook(() => useUrlValidator())

            expect(result.current.validateUrlIdentifier('valid-room')).toBe(
                true
            )
            expect(result.current.validateUrlIdentifier('room_123')).toBe(true)
            expect(result.current.validateUrlIdentifier('MyRoom2024')).toBe(
                true
            )
            expect(result.current.validateUrlIdentifier('a')).toBe(true)
        })

        it('should return false for identifiers that are too long', () => {
            const { result } = renderHook(() => useUrlValidator())
            const longIdentifier = 'a'.repeat(51)

            expect(result.current.validateUrlIdentifier(longIdentifier)).toBe(
                false
            )
        })

        it('should return false for identifiers with invalid characters', () => {
            const { result } = renderHook(() => useUrlValidator())

            expect(
                result.current.validateUrlIdentifier('room with spaces')
            ).toBe(false)
            expect(result.current.validateUrlIdentifier('room@email')).toBe(
                false
            )
            expect(result.current.validateUrlIdentifier('room#hash')).toBe(
                false
            )
            expect(result.current.validateUrlIdentifier('room.dot')).toBe(false)
            expect(result.current.validateUrlIdentifier('room/slash')).toBe(
                false
            )
        })

        it('should return false for identifiers starting or ending with hyphens or underscores', () => {
            const { result } = renderHook(() => useUrlValidator())

            expect(result.current.validateUrlIdentifier('-room')).toBe(false)
            expect(result.current.validateUrlIdentifier('room-')).toBe(false)
            expect(result.current.validateUrlIdentifier('_room')).toBe(false)
            expect(result.current.validateUrlIdentifier('room_')).toBe(false)
        })

        it('should return true for identifiers with hyphens or underscores in the middle', () => {
            const { result } = renderHook(() => useUrlValidator())

            expect(result.current.validateUrlIdentifier('my-room')).toBe(true)
            expect(result.current.validateUrlIdentifier('my_room')).toBe(true)
            expect(result.current.validateUrlIdentifier('a-b-c')).toBe(true)
            expect(result.current.validateUrlIdentifier('a_b_c')).toBe(true)
        })
    })

    describe('checkIdentifierAvailability', () => {
        it('should reject for empty or whitespace-only identifier and not set checking', async () => {
            const { result } = renderHook(() => useUrlValidator())

            await act(async () => {
                await expect(
                    result.current.checkIdentifierAvailability('')
                ).rejects.toThrow('EMPTY_IDENTIFIER')
            })
            expect(result.current.isChecking).toBe(false)

            await act(async () => {
                await expect(
                    result.current.checkIdentifierAvailability('   ')
                ).rejects.toThrow('EMPTY_IDENTIFIER')
            })
            expect(result.current.isChecking).toBe(false)
        })

        it('should reject for invalid identifier', async () => {
            const { result } = renderHook(() => useUrlValidator())

            await act(async () => {
                await expect(
                    result.current.checkIdentifierAvailability('invalid room')
                ).rejects.toThrow('INVALID_IDENTIFIER')
            })
            expect(result.current.isChecking).toBe(false)
        })

        it('should return true when identifier is not taken', async () => {
            const mockSelect = vi.fn().mockReturnThis()
            const mockEq = vi.fn().mockResolvedValue({
                count: 0,
                error: null
            })

            mockSupabase.from.mockReturnValue({
                select: mockSelect
            } as any)
            mockSelect.mockReturnValue({
                eq: mockEq
            } as any)

            const { result } = renderHook(() => useUrlValidator())

            let ret = false
            await act(async () => {
                ret =
                    await result.current.checkIdentifierAvailability(
                        'available-room'
                    )
            })

            expect(ret).toBe(true)
            expect(mockSupabase.from).toHaveBeenCalledWith('room_info')
            expect(mockSelect).toHaveBeenCalledWith('id', {
                count: 'exact',
                head: true
            })
            expect(mockEq).toHaveBeenCalledWith(
                'url_identifier',
                'available-room'
            )
        })

        it('should return false when identifier is already in use', async () => {
            const mockSelect = vi.fn().mockReturnThis()
            const mockEq = vi.fn().mockResolvedValue({
                count: 1,
                error: null
            })

            mockSupabase.from.mockReturnValue({
                select: mockSelect
            } as any)
            mockSelect.mockReturnValue({
                eq: mockEq
            } as any)

            const { result } = renderHook(() => useUrlValidator())

            let ret = true
            await act(async () => {
                ret =
                    await result.current.checkIdentifierAvailability(
                        'taken-room'
                    )
            })

            expect(ret).toBe(false)
        })

        it('should reject when supabase returns an error', async () => {
            const mockError = new Error('Database connection failed')
            const mockSelect = vi.fn().mockReturnThis()
            const mockEq = vi.fn().mockResolvedValue({
                count: null,
                error: mockError
            })

            mockSupabase.from.mockReturnValue({
                select: mockSelect
            } as any)
            mockSelect.mockReturnValue({
                eq: mockEq
            } as any)

            const { result } = renderHook(() => useUrlValidator())

            await act(async () => {
                await expect(
                    result.current.checkIdentifierAvailability('error-room')
                ).rejects.toBe(mockError)
            })

            expect((logger as any).error).toHaveBeenCalledWith(
                'Error checking identifier:',
                mockError
            )
        })

        it('should reject on unexpected errors', async () => {
            const mockError = new Error('Unexpected error')
            const mockSelect = vi.fn().mockReturnThis()
            const mockEq = vi.fn().mockRejectedValue(mockError)

            mockSupabase.from.mockReturnValue({
                select: mockSelect
            } as any)
            mockSelect.mockReturnValue({
                eq: mockEq
            } as any)

            const { result } = renderHook(() => useUrlValidator())

            await act(async () => {
                await expect(
                    result.current.checkIdentifierAvailability('error-room')
                ).rejects.toBe(mockError)
            })

            expect((logger as any).error).toHaveBeenCalledWith(
                'Error checking identifier:',
                mockError
            )
        })
    })

    describe('hook state management', () => {
        it('should initialize with isChecking false', () => {
            const { result } = renderHook(() => useUrlValidator())
            expect(result.current.isChecking).toBe(false)
        })
    })

    describe('integration tests', () => {
        it('should return true when identifier becomes available', async () => {
            const mockSelect = vi.fn().mockReturnThis()
            const mockEq = vi.fn().mockResolvedValue({
                count: 0,
                error: null
            })

            mockSupabase.from.mockReturnValue({
                select: mockSelect
            } as any)
            mockSelect.mockReturnValue({
                eq: mockEq
            } as any)

            const { result } = renderHook(() => useUrlValidator())

            let ret = false
            await act(async () => {
                ret =
                    await result.current.checkIdentifierAvailability(
                        'test-room'
                    )
            })

            expect(ret).toBe(true)
        })
    })
})
