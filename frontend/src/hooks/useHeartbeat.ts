import { useCallback, useRef, useEffect } from 'react'
import { logger } from '@/logger'

export interface OpenMeterListenerEvent {
    livekitRoomId: string | null
    durationSecs: number
    sourceLanguage: string
    targetLanguage: string | null
}

export interface UseHeartbeatOptions {
    interval?: number
    enabled?: boolean
}

export interface UseHeartbeatReturn {
    startHeartbeat: () => void
    stopHeartbeat: () => void
    updateListenerData: (data: Partial<OpenMeterListenerEvent>) => void
}

export function useHeartbeat(
    initialData: OpenMeterListenerEvent,
    options: UseHeartbeatOptions = {}
): UseHeartbeatReturn {
    const { interval = 60000, enabled = true } = options

    // Refs
    const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null)
    const omListenerEventRef = useRef<OpenMeterListenerEvent>(initialData)

    const startHeartbeat = useCallback(() => {
        if (!enabled) return

        if (heartbeatIntervalRef.current) {
            clearInterval(heartbeatIntervalRef.current)
        }

        heartbeatIntervalRef.current = setInterval(async () => {
            try {
                const event = omListenerEventRef.current

                const response = await fetch('/api/listener/heartbeat', {
                    method: 'POST',
                    credentials: 'include',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        sourceLanguage: event.sourceLanguage,
                        targetLanguage: event.targetLanguage,
                        livekitRoomId: event.livekitRoomId,
                        durationSecs: event.durationSecs
                    })
                })

                if (!response.ok) {
                    logger.warn(
                        '💓 HEARTBEAT HOOK: Heartbeat failed with status:',
                        response.status
                    )
                }
            } catch (error) {
                logger.error('💔 HEARTBEAT HOOK: Heartbeat error:', error)
            }
        }, interval)

        logger.debug(
            '💓 HEARTBEAT HOOK: Heartbeat started with interval:',
            interval
        )
    }, [interval, enabled])

    const stopHeartbeat = useCallback(() => {
        if (heartbeatIntervalRef.current) {
            clearInterval(heartbeatIntervalRef.current)
            heartbeatIntervalRef.current = null
            logger.debug('💔 HEARTBEAT HOOK: Heartbeat stopped')
        }
    }, [])

    const updateListenerData = useCallback(
        (data: Partial<OpenMeterListenerEvent>) => {
            omListenerEventRef.current = {
                ...omListenerEventRef.current,
                ...data
            }
        },
        []
    )

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            stopHeartbeat()
        }
    }, [stopHeartbeat])

    return {
        startHeartbeat,
        stopHeartbeat,
        updateListenerData
    }
}
