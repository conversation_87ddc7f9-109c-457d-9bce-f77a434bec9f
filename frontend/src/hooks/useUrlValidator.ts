import { useState, useCallback } from 'react'
import { supabase } from '@/lib/supabase'
import { logger } from '@/logger'

export function useUrlValidator() {
    const [isChecking, setIsChecking] = useState(false)

    const validateUrlIdentifier = (identifier: string): boolean => {
        if (identifier.length > 50) return false
        const urlSafePattern = /^[a-zA-Z0-9_-]+$/
        if (!urlSafePattern.test(identifier)) return false
        if (
            identifier.startsWith('-') ||
            identifier.startsWith('_') ||
            identifier.endsWith('-') ||
            identifier.endsWith('_')
        )
            return false
        return true
    }

    const checkIdentifierAvailability = useCallback(
        async (identifier: string): Promise<boolean> => {
            if (!identifier.trim()) {
                throw new Error('EMPTY_IDENTIFIER')
            }

            if (!validateUrlIdentifier(identifier)) {
                throw new Error('INVALID_IDENTIFIER')
            }

            setIsChecking(true)

            try {
                const { count, error: supabaseError } = await supabase
                    .from('room_info')
                    .select('id', { count: 'exact', head: true })
                    .eq('url_identifier', identifier)

                if (supabaseError) throw supabaseError

                return (count ?? 0) === 0
            } catch (error) {
                logger.error('Error checking identifier:', error)
                throw error
            } finally {
                setIsChecking(false)
            }
        },
        []
    )

    return {
        isChecking,
        validateUrlIdentifier,
        checkIdentifierAvailability
    }
}
