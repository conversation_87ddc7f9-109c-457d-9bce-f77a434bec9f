// TODO: Improvements to the API:
// - Add listRooms and getRoom.

import { useState, useCallback } from 'react'
import { AuthenticatedFetch } from '@/hooks/useAuth'
import { logger } from '@/logger'
import { supabase } from '@/lib/supabase'
import type { Room } from '@/types/room'

export interface CreateRoomParams {
    title: string
    description: string | null
    url_identifier: string | null
}

export interface RoomDetails extends Omit<CreateRoomParams, 'url_identifier'> {
    url_identifier: string
    room_id: number
}

export function useRoomManager(
    authenticatedFetch: AuthenticatedFetch,
    userId: string
) {
    // Server-side room management state
    const [loading, setLoading] = useState<boolean>(false)

    // Validate room creation data
    const _validateRoomData = (data: CreateRoomParams): string | null => {
        if (!data.title.trim()) return 'Room title is required'
        if (data.title.trim().length < 3)
            return 'Room title must be at least 3 characters'
        if (data.title.trim().length > 100)
            return 'Room title must be less than 100 characters'
        return null
    }

    const createRoom = useCallback(
        async (params: CreateRoomParams): Promise<RoomDetails> => {
            const validationError = _validateRoomData(params)
            if (validationError) {
                throw new Error('Validation error: ' + validationError)
            }
            setLoading(true)

            try {
                logger.debug('Creating room with params:', params)

                // Prepare the request payload with trimming and null handling
                const payload = {
                    title: params.title.trim(),
                    description: params.description?.trim() || null,
                    url_identifier: params.url_identifier?.trim() || null
                }

                const createResult = await authenticatedFetch(
                    '/api/broadcaster/create-room',
                    {
                        method: 'POST',
                        body: JSON.stringify(payload)
                    }
                )

                if (!createResult.success || !createResult.response) {
                    const errorMessage =
                        createResult.error || 'Failed to create room'
                    logger.error('Room creation failed:', errorMessage)
                    throw new Error(errorMessage)
                }

                // Only try to parse JSON if the response is successful
                if (!createResult.response.ok) {
                    const errorText = await createResult.response.text()
                    logger.error(
                        'Room creation HTTP error:',
                        createResult.response.status,
                        errorText
                    )
                    throw new Error(
                        `HTTP ${createResult.response.status}: ${errorText}`
                    )
                }

                const roomApiResponse = await createResult.response.json()
                logger.debug('Room created successfully:', roomApiResponse)

                // Map the backend response to our frontend interface
                const roomDetails: RoomDetails = {
                    title: roomApiResponse.title,
                    description: roomApiResponse.description,
                    url_identifier: roomApiResponse.url_identifier,
                    room_id: roomApiResponse.room_id
                }

                return roomDetails
            } catch (err: any) {
                throw err
            } finally {
                setLoading(false)
            }
        },
        [authenticatedFetch]
    )

    const deleteRoom = useCallback(
        async (room: RoomDetails): Promise<boolean> => {
            setLoading(true)

            try {
                const roomId = room.room_id
                if (!roomId) {
                    logger.error('Room id does not exist')
                    throw new Error('Room id does not exists.')
                }

                const result = await authenticatedFetch(
                    `/api/broadcaster/delete-room/${roomId}`,
                    {
                        method: 'DELETE'
                    }
                )

                if (!result.success) {
                    const errorMessage = result.error || 'Failed to delete room'
                    logger.error('Room deletion failed:', errorMessage)
                    throw new Error(errorMessage)
                }

                logger.info('Room deleted successfully:', roomId)
                return true
            } catch (err: any) {
                const errorMessage = err.message || 'Failed to delete room'
                logger.error('Room deletion error:', err)
                throw new Error(errorMessage)
            } finally {
                setLoading(false)
            }
        },
        [authenticatedFetch]
    )

    const getRoomList = useCallback(async (): Promise<Room[]> => {
        setLoading(true)
        try {
            logger.debug('Fetching rooms list from room_info for current user')

            const { data, error } = await supabase
                .from('room_info')
                .select('id, title, url_identifier, status, created_at')
                .eq('user_id', userId)
                .order('id', { ascending: false })

            if (error) {
                logger.error('Failed to fetch rooms:', error)
                throw new Error(error.message || 'Failed to fetch rooms')
            }

            return (data as Room[]) ?? []
        } catch (err) {
            logger.error('Unexpected error fetching rooms:', err)
            const e = err instanceof Error ? err : new Error(String(err))
            throw e
        } finally {
            setLoading(false)
        }
    }, [userId])

    return {
        loading,

        // API
        createRoom,
        deleteRoom,
        getRoomList
    }
}
