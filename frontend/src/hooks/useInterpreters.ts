import { useCallback, useState } from 'react'
import { logger } from '@/logger'
import { AuthenticatedFetch } from '@/hooks/useAuth'
import { Room, RoomEvent, RemoteParticipant } from 'livekit-client'

// Types
export type InterpreterStatus = 'pending' | 'active' | 'removing' | 'removed'

export interface Interpreter {
    id: string
    sourceLanguage: string
    targetLanguage: string
    status: InterpreterStatus
    // Remove setStatus - we'll manage this differently
}

export interface AddInterpreterParams {
    sessionId: string
    sourceLanguage: string
    targetLanguage: string
    livekitRoomId: string
}

export function useInterpreters(authenticatedFetch: AuthenticatedFetch) {
    // State
    const [loading, setLoading] = useState<boolean>(false)

    const addInterpreter = useCallback(
        async (
            params: AddInterpreterParams,
            livekitRoom: Room,
            onStatusChange: (
                interpreterId: string,
                status: InterpreterStatus
            ) => void
        ): Promise<Interpreter> => {
            setLoading(true)

            try {
                const result = await authenticatedFetch(
                    `/api/add-interpreter/${params.sessionId}`,
                    {
                        method: 'POST',
                        body: JSON.stringify({
                            source_language: params.sourceLanguage,
                            target_language: params.targetLanguage,
                            livekit_room_id: params.livekitRoomId
                        })
                    }
                )

                if (!result.success || !result.response) {
                    throw new Error(result.error || 'Failed to add interpreter')
                }

                const responseData = await result.response.json()
                logger.debug('Interpreter added successfully:', responseData)

                const interpreter: Interpreter = {
                    id: responseData.interpreter_id,
                    sourceLanguage: params.sourceLanguage,
                    targetLanguage: params.targetLanguage,
                    status: 'pending'
                }

                // Set up event listeners for status changes
                const handleParticipantConnected = (
                    participant: RemoteParticipant
                ) => {
                    if (participant.identity === interpreter.id) {
                        onStatusChange(interpreter.id, 'active')
                    }
                }

                const handleParticipantDisconnected = (
                    participant: RemoteParticipant
                ) => {
                    if (participant.identity === interpreter.id) {
                        onStatusChange(interpreter.id, 'removed')
                    }
                }

                livekitRoom.on(
                    RoomEvent.ParticipantConnected,
                    handleParticipantConnected
                )
                livekitRoom.on(
                    RoomEvent.ParticipantDisconnected,
                    handleParticipantDisconnected
                )

                // Store cleanup functions on the interpreter object for later removal
                ;(interpreter as any).cleanup = () => {
                    livekitRoom.off(
                        RoomEvent.ParticipantConnected,
                        handleParticipantConnected
                    )
                    livekitRoom.off(
                        RoomEvent.ParticipantDisconnected,
                        handleParticipantDisconnected
                    )
                }

                return interpreter
            } catch (err: any) {
                throw err
            } finally {
                setLoading(false)
            }
        },
        [authenticatedFetch]
    )

    // Remove interpreter
    const removeInterpreter = useCallback(
        async (interpreterId: string): Promise<void> => {
            setLoading(true)

            try {
                logger.debug('Removing interpreter:', { interpreterId })

                const result = await authenticatedFetch(
                    `/api/delete-interpreter/${interpreterId}`,
                    { method: 'DELETE' }
                )

                if (!result.success) {
                    throw new Error(
                        result.error || 'Failed to remove interpreter'
                    )
                }
            } catch (err: any) {
                throw err
            } finally {
                setLoading(false)
            }
        },
        [authenticatedFetch]
    )

    return {
        loading,

        // API
        addInterpreter,
        removeInterpreter
    }
}
