/**
 * useBroadcast - start & stop broadcasting into the livekit room.
 */

import { useState, useCallback } from 'react'
import { logger } from '@/logger'
import { SessionDetails } from '@/hooks/useSessionManager'
import {
    Track,
    LocalAudioTrack,
    createLocalAudioTrack,
    AudioPresets
} from 'livekit-client'

export interface BroadcastDetails extends SessionDetails {
    _audioTrack?: LocalAudioTrack
}

export type AudioSource = 'microphone' | 'browser_audio'

export type BroadcastState = 'idle' | 'preparing' | 'broadcasting' | 'stopping'

export function useBroadcast() {
    // Broadcast state
    const [broadcastState, setBroadcastState] = useState<BroadcastState>('idle')
    const [broadcastDetails, setBroadcastDetails] =
        useState<BroadcastDetails | null>(null)

    const startBroadcast = useCallback(
        async (
            sessionDetails: SessionDetails,
            audioSource: AudioSource
        ): Promise<void> => {
            try {
                setBroadcastState('preparing')
                if (sessionDetails.livekit_room == null) {
                    throw new Error('Room has not been created.')
                }

                // Check if we already have a track published and stop them first if any.
                const existingAudioTrack =
                    sessionDetails.livekit_room?.localParticipant
                        .audioTrackPublications
                for (const publication of existingAudioTrack.values()) {
                    console.debug(
                        '🐛 DEBUG: Unpublishing existing track:',
                        publication.trackName
                    )
                    await sessionDetails.livekit_room.localParticipant.unpublishTrack(
                        publication.track!
                    )
                }

                let audioTrack: LocalAudioTrack | undefined
                if (audioSource == 'microphone') {
                    audioTrack = await createLocalAudioTrack({
                        echoCancellation: false,
                        noiseSuppression: false,
                        autoGainControl: false,
                        channelCount: 2,
                        sampleRate: 48000,
                        sampleSize: 16
                    })
                } else {
                    const screenTracks =
                        await sessionDetails.livekit_room?.localParticipant.createScreenTracks(
                            {
                                audio: true
                            }
                        )
                    audioTrack = screenTracks?.find(
                        (track: Track) => track.kind === Track.Kind.Audio
                    ) as LocalAudioTrack | undefined

                    if (!audioTrack) {
                        throw new Error(
                            'No audio track was created. Make sure to select "Share tab audio" when prompted.'
                        )
                    }
                }

                await sessionDetails.livekit_room?.localParticipant.publishTrack(
                    audioTrack,
                    {
                        audioPreset: AudioPresets.speech,
                        dtx: false,
                        red: true,
                        name: audioSource
                    }
                )

                setBroadcastState('broadcasting')
                setBroadcastDetails({
                    ...sessionDetails,
                    _audioTrack: audioTrack
                })
            } catch (error) {
                logger.error('Failed to start broadcast:', error)
                setBroadcastState('idle')
                throw error
            }
        },
        []
    )

    // Stop broadcasting
    const stopBroadcast = useCallback(async (): Promise<void> => {
        try {
            // Unpublish the audio track if it exists
            if (
                broadcastDetails &&
                broadcastDetails._audioTrack &&
                broadcastDetails.livekit_room?.localParticipant
            ) {
                setBroadcastState('stopping')
                await broadcastDetails.livekit_room.localParticipant.unpublishTrack(
                    broadcastDetails._audioTrack,
                    true
                )

                setBroadcastDetails({
                    ...broadcastDetails,
                    _audioTrack: undefined
                })
            }
        } catch (error) {
            logger.error('Failed to stop broadcast:', error)
            throw error
        } finally {
            setBroadcastState('idle')
        }
    }, [broadcastDetails])

    return {
        // State
        broadcastState,
        broadcastDetails,

        // API
        startBroadcast,
        stopBroadcast
    }
}
