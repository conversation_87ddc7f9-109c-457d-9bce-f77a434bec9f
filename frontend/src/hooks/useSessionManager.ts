import { useState, useCallback } from 'react'
import { AuthenticatedFetch } from '@/hooks/useAuth'
import { Room, RoomEvent, DisconnectReason, AudioPresets } from 'livekit-client'
import { logger } from '@/logger'
import { supabase } from '@/lib/supabase'

export interface CreateSessionParams {
    room_id: number
    title: string | null
}

export interface SessionDetails {
    // Core identifiers
    session_id: string
    room_id?: number

    // List fields (present when fetched via list)
    session_title?: string | null
    session_created_at?: string
    session_status?: 'CREATED' | 'COMPLETED'
    url_identifier?: string
    room_title?: string

    // LiveKit fields (present when creating/joining)
    livekit_token?: string
    livekit_url?: string
    livekit_room_id?: string
    livekit_room?: Room
}

export function useSessionManager(
    authenticatedFetch: AuthenticatedFetch,
    userId: string
) {
    const [loading, setLoading] = useState<boolean>(false)

    const createSession = useCallback(
        async (params: CreateSessionParams): Promise<SessionDetails> => {
            setLoading(true)
            logger.debug(`Creating session room id: ${params.room_id}`)
            try {
                const result = await authenticatedFetch(
                    `/api/broadcaster/create-session`,
                    {
                        method: 'POST',
                        body: JSON.stringify(params)
                    }
                )

                if (!result.success || !result.response) {
                    const errorMessage =
                        result.error || 'Failed to create session'
                    logger.error('Session creation failed:', errorMessage)
                    throw new Error(errorMessage)
                }

                if (!result.response.ok) {
                    const errorText = await result.response.text()
                    logger.error(
                        'Session creation HTTP error:',
                        result.response.status,
                        errorText
                    )
                    throw new Error(
                        `HTTP ${result.response.status}: ${errorText}`
                    )
                }

                const response = await result.response.json()
                logger.debug('Session created successfully:', response)
                return {
                    ...response,
                    room_id: params.room_id
                }
            } finally {
                setLoading(false)
            }
        },
        [authenticatedFetch]
    )

    const lookupSession = useCallback(
        async (sessionId: string): Promise<SessionDetails> => {
            setLoading(true)
            logger.debug(`Fetching session by id: ${sessionId}`)
            try {
                const result = await authenticatedFetch(
                    `/api/broadcaster/session/${sessionId}`,
                    {
                        method: 'GET'
                    }
                )

                if (!result.success || !result.response) {
                    const errorMessage =
                        result.error || 'Failed to fetch session'
                    logger.error('Fetch session failed:', errorMessage)
                    throw new Error(errorMessage)
                }

                if (!result.response.ok) {
                    const errorText = await result.response.text()
                    logger.error(
                        'Fetch session HTTP error:',
                        result.response.status,
                        errorText
                    )
                    throw new Error(
                        `HTTP ${result.response.status}: ${errorText}`
                    )
                }

                const response = await result.response.json()
                logger.debug('Fetched session successfully:', response)
                return response as SessionDetails
            } finally {
                setLoading(false)
            }
        },
        [authenticatedFetch]
    )

    const joinSession = useCallback(
        async (sessionDetails: SessionDetails): Promise<SessionDetails> => {
            setLoading(true)

            try {
                const { livekit_url, livekit_token } = sessionDetails
                if (!livekit_url || !livekit_token) {
                    throw new Error('Missing LiveKit credentials')
                }

                logger.info('ROOM: Connecting to room...')
                const lkRoom = new Room({
                    adaptiveStream: true,
                    dynacast: true,
                    publishDefaults: {
                        audioPreset: AudioPresets.speech,
                        dtx: false,
                        red: true
                    }
                })

                lkRoom.on(
                    RoomEvent.Disconnected,
                    (reason?: DisconnectReason) => {
                        logger.debug('ROOM: Room disconnected:', reason)
                    }
                )

                lkRoom.on(RoomEvent.Reconnecting, () => {
                    logger.debug('ROOM: Reconnecting...')
                })

                lkRoom.on(RoomEvent.Reconnected, () => {
                    logger.debug('ROOM: Reconnected')
                })

                await lkRoom.connect(livekit_url, livekit_token)
                logger.info('ROOM: Connected to room successfully')
                return { ...sessionDetails, livekit_room: lkRoom }
            } catch (err: any) {
                const errorMessage = err.message || 'Failed to join room'
                logger.error('Failed to join room', err)
                throw new Error(errorMessage)
            } finally {
                setLoading(false)
            }
        },
        []
    )

    const leaveSession = useCallback(
        async (sessionDetails: SessionDetails): Promise<boolean> => {
            setLoading(true)
            logger.info('ROOM: Disconnecting...')
            try {
                await sessionDetails.livekit_room?.disconnect()
                return true
            } finally {
                setLoading(false)
            }
        },
        []
    )

    const endSession = useCallback(
        async (sessionDetails: SessionDetails): Promise<boolean> => {
            setLoading(true)
            logger.debug(
                `Ending session room id: ${sessionDetails.livekit_room_id}`
            )
            try {
                const result = await authenticatedFetch(
                    `/api/broadcaster/end-session/${sessionDetails.session_id}`,
                    {
                        method: 'POST',
                        body: JSON.stringify({
                            room_id: sessionDetails.room_id
                        })
                    }
                )

                if (!result.success || !result.response) {
                    const errorMessage = result.error || 'Failed to end session'
                    logger.error('Ending session failed:', errorMessage)
                    throw new Error(errorMessage)
                }

                if (!result.response.ok) {
                    const errorText = await result.response.text()
                    logger.error(
                        'Ending session HTTP error:',
                        result.response.status,
                        errorText
                    )
                    throw new Error(
                        `HTTP ${result.response.status}: ${errorText}`
                    )
                }

                const response = await result.response.json()
                logger.debug('Ending session successfully:', response)
                return true
            } finally {
                setLoading(false)
            }
        },
        [authenticatedFetch]
    )

    const getSessionList = useCallback(async (): Promise<SessionDetails[]> => {
        setLoading(true)
        try {
            logger.debug('Fetching sessions list via session_room_view')

            const { data, error } = await supabase
                .from('session_room_view')
                .select(
                    'session_id, session_created_at, session_status, url_identifier, room_title, session_title, user_id'
                )
                .eq('user_id', userId)
                .order('session_created_at', { ascending: false })

            if (error) {
                logger.error('Failed to fetch sessions (join):', error)
                throw new Error(error.message || 'Failed to fetch sessions')
            }

            const result: SessionDetails[] =
                (data as any[] | null)?.map((row: any) => ({
                    session_id: row.session_id,
                    session_title: row.session_title ?? null,
                    session_created_at: row.session_created_at,
                    session_status: row.session_status,
                    url_identifier: row.url_identifier,
                    room_title: row.room_title
                })) ?? []

            return result
        } catch (err) {
            logger.error('Unexpected error fetching sessions (join):', err)
            const e = err instanceof Error ? err : new Error(String(err))
            throw e
        } finally {
            setLoading(false)
        }
    }, [userId])

    return {
        loading,

        createSession,
        lookupSession,
        joinSession,
        leaveSession,
        endSession,
        getSessionList
    }
}
