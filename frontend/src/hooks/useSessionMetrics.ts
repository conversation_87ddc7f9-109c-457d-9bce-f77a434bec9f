/**
 * useSessionMetrics - Hook for fetching and managing session metrics
 */

import { useState, useRef, useEffect, useCallback } from 'react'
import { logger } from '@/logger'
import { AuthenticatedFetch } from '@/hooks/useAuth'

interface MeterValue {
    group_by_language_pair: Record<string, number>
    total: number
}

export interface SessionMetrics {
    ai_interpreter_seconds: MeterValue
    total_listeners_seconds: MeterValue
    total_cost: MeterValue
    listeners_count: MeterValue
    total_ai_interpreter_cost: MeterValue
    total_listeners_cost: MeterValue
}

export function useSessionMetrics(
    authenticatedFetch: AuthenticatedFetch,
    sessionId: string
) {
    const [sessionMetrics, setSessionMetrics] = useState<SessionMetrics | null>(
        null
    )
    const [loading, setLoading] = useState<boolean>(false)
    const [error, setError] = useState<string | null>(null)

    const metricsTimerRef = useRef<NodeJS.Timeout | null>(null)

    const fetchSessionMetrics = useCallback(async (): Promise<void> => {
        setLoading(true)
        setError(null)

        try {
            logger.debug(`Fetching session metrics for Session: ${sessionId}`)

            const response = await authenticatedFetch(
                `/api/query-meter/${sessionId}`
            )

            logger.debug('Session metrics response:', response)

            if (response.success && response.response) {
                const metrics = await response.response.json()
                logger.debug('Session metrics data:', metrics)
                setSessionMetrics({ ...metrics })
            } else {
                const errorMsg = `Failed to fetch session metrics: ${response.error || 'Unknown error'}`
                logger.error('Session metrics fetch unsuccessful:', response)
                setError(errorMsg)
            }
        } catch (error) {
            const errorMessage = `Failed to fetch session metrics: ${error}`
            logger.error(errorMessage, error)
            setError(errorMessage)
        } finally {
            setLoading(false)
        }
    }, [sessionId, authenticatedFetch])

    useEffect(() => {
        logger.info('Starting metrics collection for session:', sessionId)

        // Initial fetch
        fetchSessionMetrics()

        // Set up interval
        metricsTimerRef.current = setInterval(() => {
            fetchSessionMetrics()
        }, 30000) // Update every 30 seconds

        return () => {
            if (metricsTimerRef.current) {
                clearInterval(metricsTimerRef.current)
                metricsTimerRef.current = null
            }
        }
    }, [fetchSessionMetrics])

    return {
        sessionMetrics,
        loading,
        error
    }
}
