import { useState, useCallback } from 'react'
import { supabase } from '@/lib/supabase'

export function useRoomLookup() {
    const [loading, setLoading] = useState(false)

    const getLivekitRoomId = useCallback(
        async (urlIdentifier: string): Promise<string | null> => {
            try {
                setLoading(true)

                const { data, error: supabaseError } = await supabase
                    .from('session_room_view')
                    .select('livekit_room_id')
                    .eq('url_identifier', urlIdentifier)
                    .order('session_created_at', { ascending: false })
                    .limit(1)
                    .maybeSingle()

                if (supabaseError) throw supabaseError

                return data?.livekit_room_id || null
            } finally {
                setLoading(false)
            }
        },
        []
    )

    const getRoomDetails = useCallback(
        async (
            urlIdentifier: string
        ): Promise<{ title: string; description: string } | null> => {
            try {
                setLoading(true)

                const { data, error: supabaseError } = await supabase
                    .from('room_info')
                    .select('title, description')
                    .eq('url_identifier', urlIdentifier)

                if (supabaseError) throw supabaseError

                const result = data?.[0]
                if (!result) return null

                return {
                    title: result.title,
                    description: result.description
                }
            } finally {
                setLoading(false)
            }
        },
        []
    )

    return {
        loading,

        getLivekitRoomId,
        getRoomDetails
    }
}
