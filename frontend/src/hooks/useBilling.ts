import { AuthenticatedFetch } from '@/hooks/useAuth'

export type PaymentStatus = 'SUCCEEDED' | 'PROCESSING' | 'CANCELED'

export interface PaymentHistoryItem {
    id: string
    amount_usd: number
    status: PaymentStatus
    created: number
    metadata?: Record<string, any>
}

export interface PaymentHistoryResponse {
    payments: PaymentHistoryItem[]
    total_payments: number
}

export interface UseBillingActions {
    // TODO: throw errors instead of using callbacks
    fetchBalance: (
        onError: (error: Error) => void
    ) => Promise<number | undefined>
    createPaymentIntent: (
        amount: number,
        onError: (error: Error) => void
    ) => Promise<string | undefined>
    fetchPaymentHistory: (
        onError: (error: Error) => void
    ) => Promise<PaymentHistoryResponse | undefined>
}

export function useBilling(
    authenticatedFetch: AuthenticatedFetch
): UseBillingActions {
    const fetchBalance = async (
        onError: (error: Error) => void
    ): Promise<number | undefined> => {
        try {
            const response = await authenticatedFetch(
                '/api/billing/entitlement'
            )

            if (!response.success || !response.response?.ok) {
                throw new Error('Failed to fetch balance')
            }

            const data = await response.response.json()
            return data.balance || 0
        } catch (error) {
            onError(error as Error)
            return undefined
        }
    }

    const createPaymentIntent = async (
        amount_usd: number,
        onError: (error: Error) => void
    ): Promise<string | undefined> => {
        try {
            const response = await authenticatedFetch(
                '/api/billing/create-payment-intent',
                {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ amount_usd })
                }
            )

            if (!response.success || !response.response?.ok) {
                let errorMessage = 'Failed to create payment intent'
                try {
                    const errorData = await response.response?.json()
                    errorMessage =
                        errorData?.detail || errorData?.message || errorMessage
                } catch (parseError) {
                    errorMessage = response.response?.statusText || errorMessage
                }
                throw new Error(errorMessage)
            }

            const data = await response.response.json()
            return data.client_secret
        } catch (error) {
            onError(error as Error)
            return undefined
        }
    }

    const fetchPaymentHistory = async (
        onError: (error: Error) => void
    ): Promise<PaymentHistoryResponse | undefined> => {
        try {
            const response = await authenticatedFetch('/api/billing/payments')

            if (!response.success || !response.response?.ok) {
                throw new Error('Failed to fetch payment history')
            }

            return await response.response.json()
        } catch (error) {
            onError(error as Error)
            return undefined
        }
    }

    return {
        fetchBalance,
        createPaymentIntent,
        fetchPaymentHistory
    }
}
