import { createClient } from '@supabase/supabase-js'
import { logger } from '../logger'

const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL as string
const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY as string

// Validate environment variables
if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
    logger.error(
        'Missing Supabase environment variables. Check your .env file.'
    )
}

export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)
