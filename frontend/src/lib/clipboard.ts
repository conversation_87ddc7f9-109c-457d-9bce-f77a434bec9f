import { logger } from '@/logger'
import { toast } from 'react-toastify'

export async function copyToClipboard(
    text: string,
    message: string
): Promise<void> {
    try {
        if (
            typeof navigator === 'undefined' ||
            !navigator.clipboard?.writeText
        ) {
            throw new Error('Clipboard API not available')
        }
        await navigator.clipboard.writeText(text)
        toast.success(message)
    } catch (err) {
        logger.error('Failed to copy to clipboard', err)
        toast.error('Failed to copy to clipboard')
    }
}
