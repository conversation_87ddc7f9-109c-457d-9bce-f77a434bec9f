/**
 * Entry point for the Broadcast Test React application
 */

import React from 'react'
import <PERSON>actDOM from 'react-dom/client'

import { BroadcastTestApp } from './components/BroadcastTestApp'
import { ToastContainer } from 'react-toastify'
import ErrorBoundary from './components/ErrorBoundary'
import './globals.css'

// Mount the React app
const rootElement = document.getElementById('root')

if (rootElement) {
    const root = ReactDOM.createRoot(rootElement)
    root.render(
        <React.StrictMode>
            <ErrorBoundary>
                <BroadcastTestApp />
                <ToastContainer
                    position="bottom-right"
                    autoClose={5000}
                    hideProgressBar={true}
                    newestOnTop={true}
                    closeOnClick={true}
                />
            </ErrorBoundary>
        </React.StrictMode>
    )
} else {
    console.error('Failed to find root element for React app')
}
