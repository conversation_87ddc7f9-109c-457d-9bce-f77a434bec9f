# Environment files with sensitive credentials
.env
.env.*
dot-env-template

# Git and version control (but keep README.md as it's needed by pyproject.toml)
.git
.gitignore

# Python cache and build artifacts
__pycache__
*.pyc
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Node.js (we only need package files, not node_modules since we install fresh)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary files
tmp/
temp/
*.tmp

# Any other sensitive files you might have
credentials/
*.key
*.pem
*.cert
*.crt
