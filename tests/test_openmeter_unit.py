from datetime import datetime, timezone

import pytest

from tiong.app.openmeter import check_user_entitlement, query_meter_value


@pytest.mark.asyncio
async def test_query_meter_value_ai_interpreter_seconds():
    """Test query_meter_value with ai_interpreter_seconds meter should return 102"""
    result = await query_meter_value(
        meter_slug="ai_interpreter_seconds", subject="test-customer"
    )

    assert isinstance(result, list)
    assert len(result) == 1
    assert result[0]["value"] == 102


@pytest.mark.asyncio
async def test_query_meter_value_total_cost():
    """Test query_meter_value with total_cost meter should return 22.6"""
    result = await query_meter_value(meter_slug="total_cost", subject="test-customer")

    assert isinstance(result, list)
    assert len(result) == 1
    assert abs(result[0]["value"] - 22.6) < 0.001  # Handle floating point precision


@pytest.mark.asyncio
async def test_query_meter_value_listeners_count():
    """Test query_meter_value with listeners_count meter should return 2"""
    result = await query_meter_value(
        meter_slug="listeners_count", subject="test-customer"
    )

    assert isinstance(result, list)
    assert len(result) == 1
    assert result[0]["value"] == 2


@pytest.mark.asyncio
async def test_query_meter_value_with_groupby():
    """Test query_meter_value with group_by fields"""
    result = await query_meter_value(
        meter_slug="ai_interpreter_seconds",
        subject="test-customer",
        group_by=["model", "language_pair"],
    )

    assert isinstance(result, list)
    for item in result:
        assert "value" in item
        assert isinstance(item["value"], (int, float))
        # Should have the group_by fields
        if len(result) > 0 and item["value"] > 0:
            assert "model" in item
            assert "language_pair" in item


@pytest.mark.asyncio
async def test_query_meter_value_total_listeners_seconds_exact():
    result = await query_meter_value(
        meter_slug="total_listeners_seconds", subject="test-customer"
    )

    assert isinstance(result, list)
    assert len(result) == 1
    assert result[0]["value"] == 12540


@pytest.mark.asyncio
async def test_query_meter_value_total_listeners_seconds_with_myna_filter():
    """Test query_meter_value for total_listeners_seconds with Myna filter"""
    result = await query_meter_value(
        meter_slug="total_listeners_seconds",
        subject="test-customer",
        filter_group_by={"model": ["Myna"]},
    )

    assert isinstance(result, list)
    assert len(result) == 1
    assert result[0]["value"] == 12540


@pytest.mark.asyncio
async def test_query_meter_value_total_listeners_seconds_with_model_groupby():
    """Test query_meter_value for total_listeners_seconds grouped by model"""
    result = await query_meter_value(
        meter_slug="total_listeners_seconds",
        subject="test-customer",
        group_by=["model"],
    )

    assert isinstance(result, list)
    # Find the Myna group and verify it has 12060
    myna_result = next((r for r in result if r.get("model") == "Myna"), None)
    assert myna_result is not None
    assert myna_result["value"] == 12540


@pytest.mark.asyncio
async def test_query_meter_value_date_range_1_to_4_august():
    """Test query_meter_value for 1/8/2025 to 4/8/2025"""
    result = await query_meter_value(
        meter_slug="total_listeners_seconds",
        subject="test-customer",
        from_time_utc=datetime(2025, 8, 1, tzinfo=timezone.utc),
        to_time_utc=datetime(2025, 8, 4, tzinfo=timezone.utc),
    )

    assert isinstance(result, list)
    assert len(result) == 1
    assert result[0]["value"] == 12060


@pytest.mark.asyncio
async def test_query_meter_value_date_range_1_to_11_august():
    """Test query_meter_value for 1/8/2025 to 10/8/2025"""
    result = await query_meter_value(
        meter_slug="total_listeners_seconds",
        subject="test-customer",
        from_time_utc=datetime(2025, 8, 1, tzinfo=timezone.utc),
        to_time_utc=datetime(2025, 8, 11, tzinfo=timezone.utc),
    )

    assert isinstance(result, list)
    assert len(result) == 1
    assert result[0]["value"] == 12540


@pytest.mark.asyncio
async def test_query_meter_value_groupby_language_and_model():
    result = await query_meter_value(
        meter_slug="total_listeners_seconds",
        subject="test-customer",
        group_by=["language_pair", "model"],
    )

    assert isinstance(result, list)
    assert len(result) > 0

    assert result[0]["value"] == 12540


@pytest.mark.asyncio
async def test_query_meter_value_filter_by_session_ids():
    result = await query_meter_value(
        meter_slug="total_listeners_seconds",
        subject="test-customer",
        filter_group_by={"session_id": ["test_session_001", "session-4", "session-3"]},
    )

    assert isinstance(result, list)
    assert len(result) == 1
    assert result[0]["value"] == 840  # 480 + 180 + 180


@pytest.mark.asyncio
async def test_query_meter_value_filter_and_groupby_session_ids():
    """Test query_meter_value filtered by session IDs and grouped by session_id"""
    result = await query_meter_value(
        meter_slug="total_listeners_seconds",
        subject="test-customer",
        filter_group_by={"session_id": ["test_session_001", "session-4", "session-3"]},
        group_by=["session_id"],
    )

    assert isinstance(result, list)
    assert len(result) == 3

    # Find each session and verify values
    session_001 = next(
        (r for r in result if r.get("session_id") == "test_session_001"), None
    )
    assert session_001 is not None
    assert session_001["value"] == 480

    session_4 = next((r for r in result if r.get("session_id") == "session-4"), None)
    assert session_4 is not None
    assert session_4["value"] == 180

    session_3 = next((r for r in result if r.get("session_id") == "session-3"), None)
    assert session_3 is not None
    assert session_3["value"] == 180


@pytest.mark.asyncio
async def test_query_meter_value_nonexistent_subject():
    """Test query_meter_value with subject that has no data"""
    result = await query_meter_value(
        meter_slug="ai_interpreter_seconds", subject="nonexistent-customer-999"
    )

    # Should return empty list for subject with no events
    assert isinstance(result, list)
    assert len(result) == 0


@pytest.mark.asyncio
async def test_check_user_entitlement():
    """Test check_user_entitlement with expected balance and access"""
    result = await check_user_entitlement("9bfe331c-bc05-443c-b0e2-7c7cd1d47ae0")

    assert isinstance(result, dict)
    assert result["hasAccess"] is True
    assert result["balance"] == 390
