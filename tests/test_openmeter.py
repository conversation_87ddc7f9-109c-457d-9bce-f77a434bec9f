import asyncio

from tiong.app.openmeter import send_session_usage_event


async def main():
    result = await send_session_usage_event(
        subject="customer-14",
        ai_duration_seconds=60,
        listener_count=10,
        listener_duration_seconds=60,
        model="<PERSON><PERSON>",
        language_pair="en_ar",
        session_id="s4",
    )
    print(f"Event sent: {result}")


if __name__ == "__main__":
    asyncio.run(main())
