#!/usr/bin/env python3
import asyncio

from tiong.app.openmeter import send_session_usage_event


async def main():
    n = 500  # number of events
    concurrent = True  # True for gather, False for sequential

    # Create coroutines
    coroutines = []
    for i in range(n):
        coroutines.append(
            send_session_usage_event(
                subject="test-customer5",
                model="Myna",
                language_pair="en_ar",
                session_id=f"session-{i}",
                listener_count=1,
                listener_duration_seconds=1.0,
            )
        )

    if concurrent:
        await asyncio.gather(*coroutines)
    else:
        for coro in coroutines:
            await coro


if __name__ == "__main__":
    asyncio.run(main())
