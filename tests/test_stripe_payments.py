"""
Stripe Payments Query Tool

A command-line tool for querying customer payments from Stripe.

Examples:
    # Basic usage with specified customer ID
    python tests/test_stripe_payments.py

    # Query with different customer ID
    python tests/test_stripe_payments.py --customer_id="cus_different123"

    # Enable debug mode
    python tests/test_stripe_payments.py --debug=True
"""

import fire

from tiong.app.stripe import get_customer_completed_payments


def query_payments(
    customer_id: str = "cus_SigizF8FJsyc8K",
    debug: bool = False,
):
    try:
        payments = get_customer_completed_payments(customer_id)

        print(f"Found {len(payments)} payments for customer {customer_id}:")
        print("-" * 50)

        for payment in payments:
            print(f"Payment ID: {payment['id']}")
            print(f"Amount: ${payment['amount_usd']:.2f}")
            print(f"Status: {payment['status']}")
            print(f"Created: {payment['created']}")
            if payment["metadata"]:
                print(f"Metadata: {payment['metadata']}")
            print("-" * 30)

        if debug:
            breakpoint()

    except Exception as e:
        print(f"Error retrieving payments: {e}")
        if debug:
            breakpoint()
        return None


def main():
    fire.Fire(query_payments)


if __name__ == "__main__":
    main()
