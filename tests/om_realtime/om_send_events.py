#!/usr/bin/env python3
"""
OpenMeter Event Sender

Sends events from 3 sessions spread over configurable duration to test OpenMeter
real-time ingestion.
- Session 1: 1 listener + 4 AI instances (one per language pair)
- Session 2: 100 listeners + 4 AI instances
- Session 3: 1000 listeners + 4 AI instances
- Language pairs: en_ar, en_es, fr_en, de_en

Examples:
    # Basic usage with default parameters (60 seconds, test-customer)
    python tests/om_realtime/om_send_events.py

    # Custom subject
    python tests/om_realtime/om_send_events.py --subject="customer-123"

    # Custom duration (120 seconds)
    python tests/om_realtime/om_send_events.py --duration_seconds=120

    # Custom subject and duration
    python tests/om_realtime/om_send_events.py --subject="customer-123" \
        --duration_seconds=30
"""

import asyncio
import random

import fire

from tiong.app.openmeter import send_session_usage_event


async def send_events(
    subject: str = "c-test", duration_seconds: int = 60, model: str = "Myna"
):
    """
    Send events from 3 sessions spread over specified duration.

    Args:
        subject: Customer/user identifier
        duration_seconds: Duration to spread events over
        model: AI model name to use in events
    """

    # Available language pairs (reduced to 4)
    language_pairs = ["en_ar", "en_es", "fr_en", "de_en"]

    # Session configurations - number represents listeners per session
    sessions = [
        {"id": "session-1", "listeners": 1},
        {"id": "session-2", "listeners": 100},
        {"id": "session-3", "listeners": 1000},
    ]

    # Create event schedule
    events_to_send = []
    for session in sessions:
        # Add one event per listener (each with different language pair)
        for i in range(session["listeners"]):
            language_pair = language_pairs[
                i % len(language_pairs)
            ]  # Cycle through language pairs
            events_to_send.append(
                {
                    "session_id": session["id"],
                    "language_pair": language_pair,
                    "listener_count": 1,
                    "listener_duration_seconds": float(duration_seconds),
                    "ai_duration_seconds": 0.0,
                }
            )

        # Add one AI event per language pair per session
        for language_pair in language_pairs:
            events_to_send.append(
                {
                    "session_id": session["id"],
                    "language_pair": language_pair,
                    "listener_count": 0,
                    "listener_duration_seconds": 0.0,
                    "ai_duration_seconds": float(duration_seconds),
                }
            )

    # Shuffle events to make timing more realistic
    random.shuffle(events_to_send)

    total_events = len(events_to_send)
    interval = duration_seconds / total_events  # seconds between events

    print(
        f"🚀 Sending {total_events} events over {duration_seconds} seconds "
        f"(interval: {interval:.3f}s)"
    )
    for i, session in enumerate(sessions, 1):
        listener_events = session["listeners"]
        ai_events = len(language_pairs)  # One AI event per language pair
        total_session_events = listener_events + ai_events
        print(
            f"   📊 Session {i}: {total_session_events} events ({listener_events} "
            f"listener + {ai_events} AI)"
        )
    print()  # Empty line before event log

    # Create tasks for all events with their delays
    tasks = []
    for i, event in enumerate(events_to_send):
        delay = i * interval
        task = asyncio.create_task(
            send_event_with_delay(event, delay, i + 1, total_events, subject, model)
        )
        tasks.append(task)

    # Wait for all events to be sent
    await asyncio.gather(*tasks)

    print(f"\n✅ Finished sending {total_events} events")


async def send_event_with_delay(event, delay, event_num, total_events, subject, model):
    """Send a single event after a delay"""
    await asyncio.sleep(delay)

    # Determine event type for logging
    event_type = "AI" if event["ai_duration_seconds"] > 0 else "Listener"
    print(
        f"[{event_num:4d}/{total_events}] {event_type:8s} | "
        f"{event['session_id']} | {event['language_pair']}"
    )

    await send_session_usage_event(
        subject=subject,
        model=model,
        language_pair=event["language_pair"],
        session_id=event["session_id"],
        ai_duration_seconds=event["ai_duration_seconds"],
        listener_duration_seconds=event["listener_duration_seconds"],
    )


def main():
    fire.Fire(send_events)


if __name__ == "__main__":
    main()
