#!/usr/bin/env python3
"""
Minimal script to query OpenMeter every 5 seconds and display real-time
usage grouped by session and language pair.

Usage: python monitor_usage.py [subject]
"""

import asyncio
import sys
from datetime import datetime

from tiong.app.openmeter import query_meter_values


async def monitor_usage(subject: str = "test-customer"):
    """Query OpenMeter every 5 seconds and display usage by session and language pair"""

    # Meters to monitor
    meters = [
        "ai_interpreter_seconds",
        "total_listeners_seconds",
        "interpreter_session",
    ]

    print(
        f"🔍 Monitoring usage for subject '{subject}' grouped by session and language "
        "pair"
    )
    print("📊 Meters: " + ", ".join(meters))
    print("Press Ctrl+C to stop\n")

    try:
        while True:
            try:
                # Query from start of today to now
                now = datetime.now()
                from_time_utc = now.replace(
                    hour=0, minute=0, second=0, microsecond=0
                )  # Start of today
                to_time_utc = now
                timestamp = datetime.now().strftime("%H:%M:%S")

                print(f"[{timestamp}] Session and Language Pair Usage:")

                # Query each meter grouped by both session_id and language_pair
                for meter in meters:
                    result = await query_meter_values(
                        meter_slug=meter,
                        subject=subject,
                        window_size="MINUTE",
                        from_time_utc=from_time_utc,
                        to_time_utc=to_time_utc,
                        group_by=["session_id", "language_pair"],
                    )

                    print(f"  📈 {meter}:")

                    if result.get("data"):
                        # Group results by session_id, then by language_pair
                        session_data = {}
                        for row in result["data"]:
                            group_by = row.get("groupBy", {})
                            session_id = group_by.get("session_id", "unknown")
                            language_pair = group_by.get("language_pair", "unknown")
                            value = row.get("value", 0)

                            if session_id not in session_data:
                                session_data[session_id] = {}
                            session_data[session_id][language_pair] = (
                                session_data[session_id].get(language_pair, 0) + value
                            )

                        # Display nested by session, then language pair
                        for session_id in sorted(session_data.keys()):
                            print(f"    {session_id}:")
                            for language_pair in sorted(
                                session_data[session_id].keys()
                            ):
                                total = session_data[session_id][language_pair]
                                print(f"      {language_pair}: {total:.4f}")

                        if not session_data:
                            print("    └─ No data found")
                    else:
                        print("    └─ No data found")

                print()  # Empty line for readability

            except Exception as e:
                print(f"❌ Query error: {e}")

            await asyncio.sleep(5)

    except KeyboardInterrupt:
        print("\n👋 Monitoring stopped")


if __name__ == "__main__":
    subject = sys.argv[1] if len(sys.argv) > 1 else "test-customer"
    asyncio.run(monitor_usage(subject))
