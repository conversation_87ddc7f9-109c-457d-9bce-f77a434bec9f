"""
OpenMeter Query Tool

A command-line tool for querying meter values from OpenMeter.

Examples:
    # Basic usage with default parameters
    python tests/test_om_query.py

    # Query specific subject and meter
    python tests/test_om_query.py --subject="user-123" --meter_slug="api_calls"

    # Query with custom date range
    python tests/test_om_query.py --from_date="2025-08-01" --to_date="2025-08-10"

    # Query with grouping
    python tests/test_om_query.py --group_by="session_id,model"

    # Full example
    python tests/test_om_query.py --subject="c-10" --meter_slug="listeners_count" \
        --from_date="2025-08-03" --to_date="2025-08-10" --group_by="session_id"
"""

from datetime import datetime, timezone
from typing import Optional

import fire

from tiong.app.openmeter import query_meter_value


async def query_meter(
    subject: str = "c-10",
    meter_slug: str = "listeners_count",
    from_date: str = "2025-08-03",
    to_date: str = "2025-08-10",
    group_by: Optional[str] = "session_id",
    debug: bool = False,
):
    """
    Query meter values from OpenMeter.

    Args:
        subject: The subject to query meters for
        meter_slug: The meter slug to query
        from_date: Start date in YYYY-MM-DD format
        to_date: End date in YYYY-MM-DD format
        group_by: Comma-separated list of fields to group by
        debug: Whether to enter debugger after query
    """
    # Parse dates and ensure they are in UTC
    from_time_utc = datetime.fromisoformat(from_date).replace(tzinfo=timezone.utc)
    to_time_utc = datetime.fromisoformat(to_date).replace(tzinfo=timezone.utc)

    # Parse group_by
    group_by_list = None
    if group_by:
        group_by_list = [field.strip() for field in group_by.split(",")]

    meter_values = await query_meter_value(
        meter_slug=meter_slug,
        subject=subject,
        from_time_utc=from_time_utc,
        to_time_utc=to_time_utc,
        group_by=group_by_list,
    )

    print(meter_values)

    if debug:
        breakpoint()

    return meter_values


def main():
    fire.Fire(query_meter)


if __name__ == "__main__":
    main()
