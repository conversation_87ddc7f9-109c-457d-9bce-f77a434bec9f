# Tiong - Live Translation Platform - Quick Start Guide

A FastAPI/PostgreSQL live translation platform using LiveKit WebRTC for real-time audio streaming.

## Architecture Overview

- **Backend**: FastAPI + PostgreSQL + LiveKit
- **Frontend**: Vanilla JS + Vite
- **Database**: PostgreSQL (migrated from SQLite)
- **WebRTC**: LiveKit for audio streaming
- **Deployment**: Docker + Fly.io

## Quick Setup

1. **Environment Setup**:

```bash
cp dot-env-template .env
# Required variables (secrets only):
DATABASE_URL=********************************/db
LIVEKIT_URL=wss://your-livekit-server.com
LIVEKIT_API_KEY=your_api_key
LIVEKIT_API_SECRET=your_api_secret
ADMIN_USERNAME=admin
ADMIN_PASSWORD=secret123
```

2. **Development**:

```bash
# Terminal 1 - Backend (development mode with auto-reload)
uv run tiong

# Terminal 2 - Frontend
cd frontend && npm run dev
```

3. **Production Build**:

```bash
cd frontend && npm run build
cd .. && uv run tiong --prod
```

4. **Command Line Options**:

```bash
# Development mode (default) - auto-reload enabled
uv run tiong

# Production mode - no auto-reload, optimized for production
uv run tiong --prod

# Help
uv run tiong --help
```

## Key Endpoints

### User Endpoints

- `GET /` - Room list
- `GET /{room_id}` - Join room
- `GET /api/rooms` - Get available rooms
- `POST /api/get-listener-token/{room_id}` - Get WebRTC token
- `POST /api/heartbeat` - Keep session alive

### Admin Endpoints (Basic Auth)

- `GET /admin/stats` - Token statistics
- `POST /admin/create-room` - Create room with N tokens
- `PUT /admin/room-info/{room_id}` - Update room title/description
- `DELETE /admin/delete-room/{room_id}` - Delete entire room

### Special Pages

- `/console` - Admin console UI
- `/publisher` - Publisher/streamer interface

## Database Schema

```sql
-- PostgreSQL tables
tokens (
    id SERIAL PRIMARY KEY,
    token_value TEXT UNIQUE NOT NULL,
    assigned_cookie TEXT NULL,
    last_used_time TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    room_name TEXT NULL,
    expires_at TIMESTAMP NULL  -- Tokens expire after TTL
)

room_info (
    id SERIAL PRIMARY KEY,
    room_name TEXT UNIQUE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
)
```

## Token Management

- **Pre-generated**: LiveKit JWT tokens created when room is created
- **Session-based**: Tokens tied to browser sessions via cookies
- **Auto-cleanup**: Stale tokens reclaimed after 30 min inactivity
- **TTL Support**: Rooms/tokens expire after configured hours (default 24h)
- **Heartbeat**: Frontend sends heartbeat every minute to keep token active

## Common Tasks

### Create a New Room

```python
# POST /admin/create-room
{
    "token_count": 100,
    "ttl_hours": 24  # Room expires in 24 hours
}
# Returns: {"room_id": "Kd7FmNpQ2", "token_count": 100, ...}
```

### Add New API Endpoint

```python
# In src/tiong/app/main.py
@app.get("/api/your-endpoint")
async def your_endpoint():
    pool = await get_db_pool()
    async with pool.acquire() as conn:
        result = await conn.fetch("SELECT ...")
    return {"data": result}
```

### Add Frontend Feature

```javascript
// In frontend/src/room-list.js or listener.js
async function fetchData() {
    const response = await fetch('/api/your-endpoint');
    const data = await response.json();
    // Update UI
}
```

## Error Handling

- **Global Exception Handler**: Catches all asyncio exceptions
- **@logger.catch**: Used for background workers to prevent crashes
- **HTTP Exceptions**: Return proper status codes (404, 410, 503)
- **Logging**: Console + file logs in `logs/` directory

## Key Components

### Token Flow

1. User visits `/{room_id}`
2. Frontend requests token from `/api/get-listener-token/{room_id}`
3. Backend assigns available token to session cookie
4. Frontend connects to LiveKit using token
5. Heartbeat keeps token active
6. Token released on disconnect

### Room Lifecycle

1. Admin creates room with N tokens and TTL
2. Users join and consume tokens
3. Tokens expire after TTL hours
4. Expired tokens/rooms auto-deleted by cleanup worker

## Development Tips

- **Frontend Proxy**: Vite proxies `/api/*` to backend during dev
- **Hot Reload**: Both frontend (Vite) and backend (uvicorn) auto-reload
- **Admin Auth**: Currently disabled for debugging (see `verify_session` in admin.py)
- **Database**: PostgreSQL connection pool with asyncpg
- **Logging**: Use `logger.info()`, `logger.error()`, `logger.success()`

## Deployment

```bash
# Deploy to Fly.io
flyctl launch --no-deploy
flyctl secrets import < .env
flyctl deploy
```

## Project Structure

```text
tiong/
├── frontend/          # Vite frontend
│   ├── src/          # JS source files
│   └── *.html        # HTML templates
├── src/tiong/app/    # FastAPI backend
│   ├── main.py       # Main app + user endpoints
│   ├── admin.py      # Admin endpoints
│   └── database.py   # PostgreSQL connection
├── logs/             # Application logs
└── Dockerfile        # Production container
```

## Performance Tuning

### Critical Optimizations (High Impact)

1. **Database Connection Pool**

   - Increase pool size: `min_size=5, max_size=20` in database.py
   - Add statement caching: `statement_cache_size=100`
   - Enable prepared statements for frequent queries

2. **Query Optimization**

   - Add composite index: `CREATE INDEX idx_tokens_room_expires ON tokens(room_name, expires_at)`
   - Cache room stats with 60s TTL to avoid N+1 queries
   - Use single query with JSON aggregation for room list

3. **Frontend Bundle Optimization**

   ```javascript
   // vite.config.js - Add code splitting
   build: {
     rollupOptions: {
       output: {
         manualChunks: {
           'livekit': ['livekit-client'],
           'vendor': ['can-autoplay']
         }
       }
     }
   }
   ```

4. **API Response Caching**
   - Add Redis for caching room lists (60s TTL)
   - Implement ETags for static responses
   - Enable gzip compression: `pip install fastapi[all]`

### Medium Impact Optimizations

5. **CSS Performance**

   - Extract inline CSS to external file
   - Remove heavy gradients on mobile: `@media (max-width: 768px)`
   - Reduce animation frequency for ASCII progress

6. **WebSocket Optimization**

   - Increase heartbeat interval to 5 minutes
   - Batch heartbeat requests for multiple tabs
   - Use exponential backoff for reconnections

7. **Static Asset Caching**
   ```python
   # Add to main.py
   app.mount("/static", StaticFiles(directory=STATIC_DIR, html=True), name="static")
   # Set cache headers: Cache-Control: max-age=31536000
   ```

### Quick Wins

8. **Request Compression**

   ```python
   # Add middleware
   from fastapi.middleware.gzip import GZipMiddleware
   app.add_middleware(GZipMiddleware, minimum_size=1000)
   ```

9. **Reduce Logging Overhead**

   ```python
   # Production: Set log level to WARNING
   logger.remove()
   logger.add("logs/tiong.log", level="WARNING")
   ```

10. **Cleanup Worker Optimization**
    - Run cleanup based on token count, not fixed schedule
    - Increase cleanup interval to 15 minutes
    - Add jitter to prevent thundering herd

## Quick Debugging

- Check token stats: `curl -u admin:secret123 http://localhost:8000/admin/stats`
- View logs: `tail -f logs/tiong.log`
- Test room: Create room → Visit `/{room_id}` → Click "Listen"
- Database issues: Check `DATABASE_URL` format and connection
- Performance monitoring: Add `/metrics` endpoint with response times
