{"name": "tiong-main", "version": "1.0.0", "private": true, "scripts": {"prepare": "husky", "backend:lint": "ruff check . --fix", "backend:format": "ruff format . --check", "backend:checks": "ruff check . --fix && ruff format . --check", "frontend:format": "cd frontend && npm run format", "frontend:type-check": "cd frontend && npm run type-check", "frontend:checks": "npm run frontend:format && npm run frontend:type-check", "pretiong:local": "cd frontend && npm run build:local", "tiong:local": "shdotenv -e config/local/dot-env-local uv run tiong --prod", "supabase:start": "shdotenv -e config/local/supabase-env supabase start", "supabase:db-reset": "shdotenv -e config/local/supabase-env supabase db reset"}, "devDependencies": {"husky": "^9.1.0"}}