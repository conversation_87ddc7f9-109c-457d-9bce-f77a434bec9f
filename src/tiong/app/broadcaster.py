"""
Broadcaster-related endpoints and functionality.

This module handles all endpoints and functionality related to the broadcaster
interface, including room creation, management, token generation for broadcasters.
"""

from pathlib import Path as PathLib

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException
from fastapi.responses import HTMLResponse, JSONResponse
from loguru import logger
from pydantic import BaseModel

from .auth import Client, get_service_supabase_client, verify_and_get_client
from .interpreters import delete_interpreter
from .livekit import (
    LIVEKIT_URL,
    delete_livekit_room,
    generate_broadcaster_token,
    generate_room_id,
)
from .openmeter import query_all_meters_for_session
from .profiles import get_profile_ids

# Create router for broadcaster endpoints
broadcaster_router = APIRouter()

# Get the directory where this script is located
BASE_DIR = PathLib(__file__).parent
STATIC_DIR = BASE_DIR / "static"


@broadcaster_router.get("/broadcast", response_class=HTMLResponse)
@broadcaster_router.get("/room/{rest_of_path:path}", response_class=HTMLResponse)
@broadcaster_router.get("/billing", response_class=HTMLResponse)
def serve_broadcaster_interface_root():
    """Serve the broadcaster SPA entrypoint (broadcast.html).

    Returns the same HTML shell for client-routed paths so that:
    - GET /broadcast
    - GET /room/<...>
    - GET /billing
    all load the SPA via client-side routing. This supports OAuth redirects
    and deep links to these paths.
    """
    broadcaster_html_static = STATIC_DIR / "broadcast.html"

    if broadcaster_html_static.exists():
        with open(broadcaster_html_static, "r") as f:
            return HTMLResponse(content=f.read())
    else:
        raise HTTPException(status_code=500, detail="Internal error")


@broadcaster_router.get("/broadcast-test", response_class=HTMLResponse)
def serve_broadcast_test_interface():
    """Serve the broadcast test page (no router)"""
    broadcast_test_html_static = STATIC_DIR / "broadcast-test.html"

    if broadcast_test_html_static.exists():
        with open(broadcast_test_html_static, "r") as f:
            return HTMLResponse(content=f.read())
    else:
        raise HTTPException(status_code=500, detail="Internal error")


class CreateRoomRequest(BaseModel):
    title: str
    description: str | None = None
    url_identifier: str | None = None


@broadcaster_router.post("/api/broadcaster/create-room")
async def broadcaster_create_room(
    request: CreateRoomRequest,
    user_data: tuple[str, Client] = Depends(verify_and_get_client),
):
    """Create a new room."""
    user_id, user_client = user_data

    if not request.url_identifier:
        request.url_identifier = generate_room_id()

    row = {
        "url_identifier": request.url_identifier,
        "title": request.title,
        "description": request.description,
        "user_id": user_id,
    }

    result = user_client.table("room_info").insert(row)
    result = result.execute()

    if not result.data:
        raise HTTPException(status_code=500, detail="Failed to create room.")

    entry = {**row, "room_id": result.data[0]["id"]}
    return JSONResponse(content=entry)


class CreateSessionRequest(BaseModel):
    room_id: int | None = None
    title: str | None = None


@broadcaster_router.post("/api/broadcaster/create-session")
async def broadcaster_create_session(
    request: CreateSessionRequest,
    user_data: tuple[str, Client] = Depends(verify_and_get_client),
):
    """Create a new session given the room_id."""
    user_id, user_client = user_data

    if not request.room_id:
        raise HTTPException("room_id not specified.")

    result = user_client.table("room_info")
    result = (
        result.select("status,title,url_identifier").eq("id", request.room_id).execute()
    )
    if not result.data or result.data[0]["status"] == "BOOKED":
        raise HTTPException(status_code=409, detail="Room not available for booking.")

    # Extract room metadata for response
    room_title = result.data[0]["title"]
    url_identifier = result.data[0]["url_identifier"]

    # Book the room, for this session.
    result = user_client.table("room_info")
    result = result.update({"status": "BOOKED"})
    result = result.eq("id", request.room_id)
    result = result.execute()

    if not result.data:
        raise HTTPException(status_code=409, detail="Failed to book the room.")

    livekit_room_id = generate_room_id()
    token_jwt, url = generate_broadcaster_token(livekit_room_id)

    row = {
        "room_id": request.room_id,
        "livekit_room_id": livekit_room_id,
        "livekit_broadcaster_token": token_jwt,
        "status": "CREATED",
        "user_id": user_id,
        "title": request.title,
    }

    result = user_client.table("sessions").insert(row)
    result = result.execute()

    # TODO: The queries above should be atomic, so that when the second query fails,
    # the first query gets rolled back.
    if not result.data:
        raise HTTPException("Failed to create session.")

    session_id = result.data[0]["id"]
    return JSONResponse(
        content={
            "session_id": session_id,
            "room_id": request.room_id,
            "url_identifier": url_identifier,
            "session_title": request.title,
            "room_title": room_title,
            "livekit_room_id": livekit_room_id,
            "livekit_url": url,
            "livekit_token": token_jwt,
        }
    )


@broadcaster_router.get("/api/broadcaster/session/{session_id}")
async def broadcaster_get_session(
    session_id: str, user_data: tuple[str, Client] = Depends(verify_and_get_client)
):
    """Get details for a session by id scoped to the authenticated user."""
    user_id, user_client = user_data

    result = user_client.table("sessions")
    result = (
        result.select(
            (
                "id, room_id, livekit_room_id, "
                "livekit_broadcaster_token, "
                "title, "
                "room_info!inner(title,url_identifier)"
            )
        )
        .eq("id", session_id)
        .eq("user_id", user_id)
        .execute()
    )

    if not result.data:
        raise HTTPException(
            status_code=404, detail="Session not found or not owned by user"
        )

    session = result.data[0]
    return JSONResponse(
        content={
            "session_id": session["id"],
            "room_id": session["room_id"],
            "livekit_token": session["livekit_broadcaster_token"],
            "livekit_url": LIVEKIT_URL,
            "livekit_room_id": session["livekit_room_id"],
            "session_title": session["title"],
            "room_title": session["room_info"]["title"],
            "url_identifier": session["room_info"]["url_identifier"],
        }
    )


async def broadcaster_end_session_background(
    session_id: str, livekit_room_id: str, room_id: int, user_data: tuple[str, Client]
):
    user_id, user_client = user_data

    # Query all interpreters for this session
    result = user_client.table("ai_interpreters")
    result = result.select("uuid")
    result = result.eq("session_id", session_id)
    result = result.eq("user_id", user_id)
    result = result.execute()

    if result.data:
        logger.info(
            f"Found {len(result.data)} interpreters to delete for session {session_id}"
        )

        # Delete each interpreter
        for interpreter in result.data:
            interpreter_id = interpreter["uuid"]
            try:
                await delete_interpreter(user_data, interpreter_id)
                logger.info(f"Successfully deleted interpreter {interpreter_id}")
            except Exception as e:
                logger.error(f"Failed to delete interpreter {interpreter_id}: {str(e)}")
                # Continue with other interpreters even if one fails
        else:
            logger.info(f"No interpreters found for session {session_id}")

    # Ensures all participants will be forced to exit the session, not to
    # consume livekit resources unnecessarily.
    await delete_livekit_room(livekit_room_id)

    # TODO: Make the below queries atomic with one another.
    # Update session status to "COMPLETED"
    result = (
        user_client.table("sessions")
        .update({"status": "COMPLETED"})
        .eq("id", session_id)
        .execute()
    )

    # Update room_info status to "AVAILABLE"
    result = (
        user_client.table("room_info")
        .update({"status": "AVAILABLE"})
        .eq("id", room_id)
        .execute()
    )

    # Save session metrics
    try:
        profile_ids = get_profile_ids(user_client)
        openmeter_customer_key = profile_ids["openmeter_customer_key"]

        # TODO: make this function more robust to failures from OM
        metrics_data = await query_all_meters_for_session(
            openmeter_customer_key, session_id
        )

        # User can only view the session metrics table
        service_client = get_service_supabase_client()
        # Since all meters have the same language pairs, get them from the first meter
        first_meter = list(metrics_data.values())[0]
        language_pairs = first_meter["group_by_language_pair"].keys()
        # Create one record per language pair with all meter values
        for language_pair in language_pairs:
            record = {
                "session_id": session_id,
                "language_pair": language_pair,
            }
            # Add all meter values for this language pair
            for meter_name, meter_data in metrics_data.items():
                record[meter_name] = meter_data["group_by_language_pair"][language_pair]

            service_client.table("session_metrics").insert(record).execute()

    except Exception as e:
        logger.error(
            f"Failed to save session metrics for session {session_id}: {str(e)}"
        )


# TODO: Probably not the best API. Should be able to directly infer the
# room_id from the session_id from the tables, then, we don't need
# to pass this info.
class EndSessionRequest(BaseModel):
    room_id: int | None = None


@broadcaster_router.post("/api/broadcaster/end-session/{session_id}")
async def broadcaster_end_session(
    session_id: str,
    request: EndSessionRequest,
    background_tasks: BackgroundTasks,
    user_data: tuple[str, Client] = Depends(verify_and_get_client),
):
    user_id, user_client = user_data

    # First, get the session details to obtain the LiveKit room ID and room_id
    result = user_client.table("sessions")
    result = result.select("livekit_room_id", "status")
    result = result.eq("id", session_id).eq("user_id", user_id).execute()
    if not result.data:
        raise HTTPException(
            status_code=404, detail="Session not found or not owned by user"
        )

    session = result.data[0]
    if session["status"] == "COMPLETED":
        raise HTTPException(status_code="404", detail="Session is already completed.")

    livekit_room_id = session["livekit_room_id"]

    background_tasks.add_task(
        broadcaster_end_session_background,
        session_id,
        livekit_room_id,
        request.room_id,
        user_data,
    )

    return JSONResponse(
        content={
            "status": "processing request in the background..",
        }
    )


@broadcaster_router.delete("/api/broadcaster/delete-room/{room_id}")
async def broadcaster_delete_room(
    background_tasks: BackgroundTasks,
    user_data: tuple[str, Client] = Depends(verify_and_get_client),
):
    # TODO
    logger.warning("Not implemented.")
