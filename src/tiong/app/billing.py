"""
Billing-related endpoints and functionality.

This module handles all endpoints and functionality related to billing,
including payment processing, Stripe webhooks, user entitlements, and
the billing page interface.
"""

import os
from pathlib import Path

import stripe
from fastapi import APIRouter, Depends, HTTPException, Request
from loguru import logger
from pydantic import BaseModel, Field

from .auth import (
    get_service_supabase_client,
    verify_and_get_client_with_email,
)
from .openmeter import check_user_entitlement, create_openmeter_grant
from .profiles import get_or_create_profile
from .stripe import create_payment_intent, get_customer_completed_payments

# Create router for billing endpoints
billing_router = APIRouter()

# Get the directory where this script is located
BASE_DIR = Path(__file__).parent
STATIC_DIR = BASE_DIR / "static"

# Stripe webhook configuration
STRIPE_WEBHOOK_SECRET = os.getenv("STRIPE_WEBHOOK_SECRET", None)
if not STRIPE_WEBHOOK_SECRET:
    logger.warning(
        "STRIPE_WEBHOOK_SECRET not set - webhook signature verification disabled"
    )
else:
    logger.info("Stripe webhook signature verification enabled")


class PaymentRequest(BaseModel):
    amount_usd: float = Field(..., ge=0.50, description="Amount in USD (minimum $0.50)")


@billing_router.post("/api/billing/create-payment-intent")
async def create_payment_intent_endpoint(
    payment: PaymentRequest, auth_data=Depends(verify_and_get_client_with_email)
):
    """
    Automatically creates a Stripe customer if one doesn't exist.
    """
    try:
        user_id, client, email = auth_data

        # Get or create Stripe customer ID
        profile = await get_or_create_profile(user_id, client, email)

        # Create payment intent with the customer
        payment_intent = create_payment_intent(
            amount_usd=payment.amount_usd,
            stripe_customer_id=profile["stripe_customer_id"],
        )

        return {
            "client_secret": payment_intent.client_secret,
            "amount_usd": payment.amount_usd,
            "stripe_customer_id": profile["stripe_customer_id"],
        }

    except ValueError as e:
        logger.error(f"Payment validation error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except stripe.error.StripeError as e:
        logger.error(f"Stripe error: {e}")
        raise HTTPException(status_code=400, detail=f"Payment error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected payment error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@billing_router.post("/api/webhooks/stripe")
async def stripe_webhook(request: Request):
    """
    Stripe webhook endpoint with signature verification and duplicate handling.

    Verifies webhook signatures and processes payment events.
    On successful verification the user is given an OM grant.
    Uses successful_stripe_payment_events table to prevent duplicate processing.

    Testing: Check README.md

    ref:
    - https://docs.stripe.com/webhooks?snapshot-or-thin=snapshot#webhooks-summary
    - https://docs.stripe.com/api/v2/core/event_destinations/object
    - https://docs.stripe.com/webhooks/signature
    """
    try:
        payload = await request.body()
        if not payload:
            raise HTTPException(status_code=400, detail="Invalid payload")

        sig_header = request.headers.get("stripe-signature")
        if not sig_header:
            raise HTTPException(
                status_code=400, detail="Missing stripe-signature header"
            )

        event = stripe.Webhook.construct_event(
            payload, sig_header, STRIPE_WEBHOOK_SECRET
        )

        client = get_service_supabase_client()
        existing_event = (
            client.table("successful_stripe_payment_events")
            .select("event_id")
            .eq("event_id", event["id"])
            .execute()
        )

        if existing_event.data:
            logger.info(f"Event {event['id']} already processed, skipping")
            return {"received": True, "status": "duplicate"}

        if event["type"] == "payment_intent.succeeded":
            payment_intent = event["data"]["object"]
            stripe_customer_id = payment_intent.get("customer")

            profile = (
                client.table("profiles")
                .select("openmeter_customer_key")
                .eq("stripe_customer_id", stripe_customer_id)
                .execute()
            )

            openmeter_customer_key = (
                profile.data[0].get("openmeter_customer_key") if profile.data else None
            )
            amount_dollars = payment_intent["amount"] / 100

            if openmeter_customer_key:
                await create_openmeter_grant(
                    customer_key=openmeter_customer_key, amount=amount_dollars
                )
                logger.success(
                    f"Granted ${amount_dollars:.2f} credits to customer "
                    f"{openmeter_customer_key}"
                )

                client.table("successful_stripe_payment_events").insert(
                    {"event_id": event["id"]}
                ).execute()
            else:
                logger.error(
                    "No OpenMeter customer key found for Stripe customer "
                    f"{stripe_customer_id} in payment intent {payment_intent['id']}"
                )

        elif event["type"] == "payment_intent.payment_failed":
            payment_intent = event["data"]["object"]
            error_msg = payment_intent.get("last_payment_error", {}).get(
                "message", "Unknown error"
            )

            logger.error(f"Payment failed: {payment_intent['id']}")
            logger.error(f"Error: {error_msg}")

        else:
            logger.info(f"Unhandled event type: {event['type']}")

        return {"received": True}

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid payload: {e}")
    except stripe.error.SignatureVerificationError as e:
        raise HTTPException(status_code=400, detail=f"Invalid signature: {e}")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unexpected webhook error: {e}")


@billing_router.get("/api/billing/entitlement")
async def get_user_entitlement(auth_data=Depends(verify_and_get_client_with_email)):
    """
    Get user's current entitlement balance and access status.

    Returns the user's interpreter credits entitlement including:
    - hasAccess: Whether user can currently access the feature
    - balance: Remaining credits available
    - usage: Total consumed credits in current period
    - overage: Credits consumed beyond initial allocation
    """
    try:
        user_id, client, email = auth_data

        # Get user profile to get OpenMeter customer key
        profile = await get_or_create_profile(user_id, client, email)
        openmeter_customer_key = profile["openmeter_customer_key"]

        # Get entitlement value from OpenMeter
        entitlement = await check_user_entitlement(openmeter_customer_key)

        return entitlement

    except Exception as e:
        logger.error(f"Failed to get user entitlement: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get entitlement data: {str(e)}"
        )


@billing_router.get("/api/billing/payments")
async def get_user_payments(auth_data=Depends(verify_and_get_client_with_email)):
    try:
        user_id, client, email = auth_data

        # Get user profile to get Stripe customer ID
        profile = await get_or_create_profile(user_id, client, email)
        stripe_customer_id = profile["stripe_customer_id"]

        # Get payment history from Stripe
        payments = get_customer_completed_payments(stripe_customer_id)

        # TODO: update to handle paging
        return {"payments": payments, "total_payments": len(payments)}

    except Exception as e:
        logger.error(f"Failed to get user payments: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get payment data: {str(e)}"
        )
