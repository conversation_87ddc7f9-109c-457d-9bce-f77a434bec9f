"""
Authentication middleware and dependencies for FastAPI.
Uses Supabase for JWT validation with environment variables.
"""

import os
from typing import <PERSON>ple

import jwt
from fastapi import Header, HTTPException

from supabase import Client, create_client

SUPABASE_URL = os.getenv("SUPABASE_URL", None)
SUPABASE_ANON_KEY = os.getenv("SUPABASE_ANON_KEY", None)
SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_URL or not SUPABASE_ANON_KEY:
    raise ValueError(
        "SUPABASE_URL, SUPABASE_ANON_KEY environment variables are required."
    )
if not SERVICE_ROLE_KEY:
    raise ValueError("SUPABASE_SERVICE_ROLE_KEY environment variable is required")


def get_user_supabase_client(token: str) -> Client:
    """Create a new Supabase client for each user request"""
    client = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)
    client.auth.set_session(token, "")
    return client


def get_service_supabase_client() -> Client:
    """Create a Supabase client with service role privileges"""

    return create_client(SUPABASE_URL, SERVICE_ROLE_KEY)


async def verify_and_get_client(
    authorization: str | None = Header(None),
) -> Tuple[str, Client]:
    """Extract and validate user ID from Authorization header"""
    user_id, client, _ = await verify_and_get_client_with_email(authorization)
    return user_id, client


async def verify_and_get_client_with_email(
    authorization: str | None = Header(None),
) -> Tuple[str, Client, str]:
    """Extract and validate user ID, client, and email from Authorization header"""
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header required")

    # Extract token from "Bearer <token>"
    if not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid authentication scheme")

    token = authorization.split("Bearer ")[1]

    # Verify token first (optional but recommended)
    try:
        payload = jwt.decode(
            token,
            os.environ["SUPABASE_JWT_SECRET"],
            algorithms=["HS256"],
            audience="authenticated",
        )
        user_id = payload["sub"]
        email = payload.get("email")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")

    # Return user_id, client, and email
    client = get_user_supabase_client(token)
    return user_id, client, email
