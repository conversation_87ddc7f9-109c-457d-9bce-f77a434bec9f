import os
import secrets
import string
import uuid
from datetime import timedelta

from livekit import api

# LiveKit configuration
LIVEKIT_URL = os.getenv("LIVEKIT_URL", None)
LIVEKIT_API_KEY = os.getenv("LIVEKIT_API_KEY", None)
LIVEKIT_API_SECRET = os.getenv("LIVEKIT_API_SECRET", None)

if not LIVEKIT_URL or not LIVEKIT_API_KEY or not LIVEKIT_API_SECRET:
    raise ValueError(
        "LIVEKIT_URL, LIVEKIT_API_KEY and LIVEKIT_API_SECRET environment variables "
        "are required."
    )


def generate_room_id() -> str:
    """Generate a YouTube-like room ID (alphanumeric, 8 chars)"""
    chars = string.ascii_letters + string.digits  # a-z, A-Z, 0-9
    return "".join(secrets.choice(chars) for _ in range(8))


def generate_listener_token(room_name: str, ttl_hours: int = 24) -> str:
    identity = f"listener-{uuid.uuid4().hex[:8]}"

    token = (
        api.AccessToken(api_key=LIVEKIT_API_KEY, api_secret=LIVEKIT_API_SECRET)
        .with_identity(identity)
        .with_grants(
            api.VideoGrants(
                room_join=True,
                room=room_name,
                can_subscribe=True,
                can_publish=False,
                can_publish_data=False,
            )
        )
        .with_ttl(timedelta(hours=ttl_hours))
    )

    return token.to_jwt()


def generate_broadcaster_token(room_name: str, ttl_hours: int = 24) -> str:
    identity = "broadcaster"

    token = (
        api.AccessToken(api_key=LIVEKIT_API_KEY, api_secret=LIVEKIT_API_SECRET)
        .with_identity(identity)
        .with_grants(
            api.VideoGrants(
                room_join=True,
                room=room_name,
                can_subscribe=True,
                can_publish=True,
                can_publish_data=True,
            )
        )
        .with_ttl(timedelta(hours=ttl_hours))
    )

    return token.to_jwt(), LIVEKIT_URL


async def delete_livekit_room(room_name: str):
    livekit_client = api.LiveKitAPI(
        url=LIVEKIT_URL, api_key=LIVEKIT_API_KEY, api_secret=LIVEKIT_API_SECRET
    )

    # Create delete request and delete the room
    delete_request = api.DeleteRoomRequest(room=room_name)
    await livekit_client.room.delete_room(delete_request)

    # Clean up the client
    await livekit_client.aclose()
