import uuid
from pathlib import Path as PathLib

from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from fastapi.responses import HTMLResponse, JSONResponse
from loguru import logger
from pydantic import BaseModel

from .auth import get_service_supabase_client
from .livekit import LIVEKIT_URL, generate_listener_token
from .openmeter import send_session_usage_event

# Create router for listener endpoints
listener_router = APIRouter()

SESSION_COOKIE_MAX_AGE = 30 * 60

# Get the directory where this script is located
BASE_DIR = PathLib(__file__).parent
STATIC_DIR = BASE_DIR / "static"


@listener_router.get("/listen/{room_url_identifier}", response_class=HTMLResponse)
async def serve_listener_page(room_url_identifier: str):
    """Serve the listener page for a specific room"""
    # Serve the listener page
    # Serve the built listener.html from static directory
    listener_html_static = STATIC_DIR / "listen.html"
    listener_html_frontend = BASE_DIR.parent.parent.parent / "frontend" / "listen.html"

    if listener_html_static.exists():
        with open(listener_html_static, "r") as f:
            return HTMLResponse(content=f.read())
    elif listener_html_frontend.exists():
        with open(listener_html_frontend, "r") as f:
            return HTMLResponse(content=f.read())
    else:
        raise HTTPException(
            status_code=500,
            detail="Listener page not found",
        )


@listener_router.get("/api/config/{room_id}")
async def get_config(room_id: str):
    """Get LiveKit configuration for frontend"""
    config = {"ws_url": LIVEKIT_URL, "room_name": room_id}
    return JSONResponse(content=config)


@listener_router.post("/api/listener/get-token/{livekit_room_id}")
async def get_listener_token(
    livekit_room_id: str, session_cookie: str = Cookie(None, alias="cookie_id")
):
    if not session_cookie:
        session_cookie = str(uuid.uuid4())

    # TODO: Enforce session expiry, ttl & max listeners & reuse tokens.

    token_value = generate_listener_token(livekit_room_id)

    # TODO: Reimplement using supabase client.
    # Insert the new token into the database
    # await conn.execute(
    #     """INSERT INTO tokens
    #           (room_name, token_value, assigned_cookie, last_used_time, expires_at)
    #        VALUES ($1, $2, $3, $4, $5)""",
    #     livekit_room_id,
    #     token_value,
    #     session_cookie,
    #     current_time,
    #     current_time + timedelta(hours=ttl_hours),
    # )

    logger.info(
        f"Generated new token for room {livekit_room_id} and session {session_cookie}"
    )
    response = JSONResponse(
        content={
            "token": token_value,
            "ws_url": LIVEKIT_URL,
        }
    )
    response.set_cookie(
        "cookie_id",
        session_cookie,
        max_age=SESSION_COOKIE_MAX_AGE,
        httponly=True,
        secure=True,
        samesite="strict",
    )
    return response


class HeartbeatRequest(BaseModel):
    """Model for heartbeat request data."""

    model: str | None = "Myna"
    sourceLanguage: str
    targetLanguage: str | None
    livekitRoomId: str
    durationSecs: int


@listener_router.post("/api/listener/heartbeat")
async def update_token_heartbeat(
    event: HeartbeatRequest,
    session_cookie: str = Cookie(None, alias="cookie_id"),
):
    if not session_cookie:
        raise HTTPException(status_code=401, detail="No session cookie")

    # Log received listener data and send to OpenMeter
    try:
        # Get service client to lookup session and user information
        client = get_service_supabase_client()

        # TODO: the two queries could be combined with a left join
        # Look up session by livekit_room_id to get user_id
        session_result = (
            client.table("sessions")
            .select("id, user_id")
            .eq("livekit_room_id", event.livekitRoomId)
            .execute()
        )

        if not session_result.data:
            raise HTTPException(
                status_code=404,
                detail=f"Session not found for livekit_room_id: {event.livekitRoomId}",
            )

        session_id = session_result.data[0]["id"]
        user_id = session_result.data[0]["user_id"]

        # Look up user profile to get OpenMeter customer key
        profile_result = (
            client.table("profiles")
            .select("openmeter_customer_key")
            .eq("id", user_id)
            .execute()
        )

        if not profile_result.data:
            raise HTTPException(
                status_code=404, detail=f"Profile not found for user_id: {user_id}"
            )

        openmeter_customer_key = profile_result.data[0]["openmeter_customer_key"]

        src_language = event.sourceLanguage
        trg_language = event.targetLanguage
        language_pair = (
            f"{src_language}_{trg_language}" if trg_language else src_language
        )

        await send_session_usage_event(
            subject=openmeter_customer_key,
            model=event.model,
            language_pair=language_pair,
            session_id=session_id,
            listener_duration_seconds=event.durationSecs,
            listener_id=session_cookie,
        )
    except Exception as e:
        logger.error(f"📡 HEARTBEAT: Error processing listener data: {e}")

    # TODO: Reimplement using supabase client.
    # pool = await get_db_pool()
    # async with pool.acquire() as conn:
    #     current_time = datetime.utcnow()

    #     # Check if token exists and is not expired
    #     token_id = await conn.fetchval(
    #         """SELECT id FROM tokens
    #            WHERE assigned_cookie = $1
    #            AND (expires_at IS NULL OR expires_at > $2)""",
    #         session_cookie,
    #         current_time,
    #     )

    #     if not token_id:
    #         # Check if token exists but is expired
    #         expired_token_id = await conn.fetchval(
    #             "SELECT id FROM tokens WHERE assigned_cookie = $1", session_cookie
    #         )

    #         if expired_token_id:
    #             # Token exists but is expired
    #             raise HTTPException(status_code=410, detail="Token has expired")
    #         else:
    #             # No token found at all
    #             raise HTTPException(status_code=404, detail="No active token found")

    #     await conn.execute(
    #         "UPDATE tokens SET last_used_time = $1 WHERE assigned_cookie = $2",
    #         current_time,
    #         session_cookie,
    #     )
    #     response = JSONResponse(
    #         content={
    #             "status": "ok",
    #         }
    #     )
    #     response.set_cookie(
    #         "session_id",
    #         session_cookie,
    #         max_age=SESSION_COOKIE_MAX_AGE,
    #         httponly=True,
    #         secure=True,
    #         samesite="strict",
    #     )
    #     return response


@listener_router.post("/api/listener/release-token")
async def release_token(session_cookie: str = Cookie(None, alias="session_id")):
    if not session_cookie:
        raise HTTPException(status_code=401, detail="No session cookie")

    # TODO: Reimplement using supabase client.
    # pool = await get_db_pool()
    # async with pool.acquire() as conn:
    #     result = await conn.execute(
    #         """UPDATE tokens
    #            SET assigned_cookie = NULL,
    #            last_used_time = NULL
    #            WHERE assigned_cookie = $1""",
    #         session_cookie,
    #     )

    #     if result.split()[-1] == "0":  # Check if no rows were updated
    #         raise HTTPException(status_code=404, detail="No active token found")

    #     return {"status": "released"}
