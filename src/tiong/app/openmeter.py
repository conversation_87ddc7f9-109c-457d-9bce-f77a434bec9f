"""
OpenMeter client library for sending usage events.
Not using offical OpenMeter SDK because it is buggy.
"""

import asyncio
from datetime import datetime, timezone
from os import environ
from typing import Optional

import httpx
from cloudevents.conversion import to_dict
from cloudevents.http import CloudEvent
from loguru import logger

# Initialize configuration
OM_COLLECTOR_ENDPOINT = environ.get("OM_COLLECTOR_ENDPOINT")
OM_TOKEN = environ.get("OM_QUERY_API_TOKEN")
OM_BASE_URL = environ.get("OM_BASE_URL")

# Feature key for interpreter credits entitlement
INTERPRETER_CREDITS_FEATURE_KEY = "interpreter_credits"

if not OM_TOKEN:
    raise ValueError("OM_QUERY_API_TOKEN environment variable is required")

if not OM_BASE_URL:
    raise ValueError("OM_BASE_URL environment variable is required")
logger.info(f"OM_BASE_URL configured to {OM_BASE_URL}")

if not OM_COLLECTOR_ENDPOINT:
    raise ValueError("OM_COLLECTOR_ENDPOINT environment variable is required")
logger.info(f"OM_COLLECTOR_ENDPOINT configured to {OM_COLLECTOR_ENDPOINT}")


async def send_session_usage_event(
    # TODO: update with Sep update see create_openmeter_customer
    subject: str,
    model: str,
    language_pair: str,
    session_id: str,
    ai_duration_seconds: float = 0,
    listener_duration_seconds: float = 0,
    listener_id: Optional[str] = None,
) -> bool:
    """Send a session usage event to OpenMeter.

    This function sends CloudEvents-compliant usage events to OpenMeter
    for billing/metering.

    Event ID is auto-generated by CloudEvents library, timestamp is auto-generated
    as current time. The implementation assumes one event per listener and one
    event per model usage.

    Args:
        subject: Customer/user identifier (e.g., "customer-14").
            should be the openmeter_customer_key in the profiles table
        model: AI model used (e.g., "Myna")
        language_pair: Language pair (e.g., "en_ar")
        session_id: Session identifier
        ai_duration_seconds: Duration of AI processing in seconds (defaults to 0)
        listener_duration_seconds: Duration listener was active in seconds
            (defaults to 0)
        listener_id: a Unique value identifying the listener; assumes that one
            event corresponds to one listener.

    Returns:
        True if event was sent successfully, False otherwise
    """
    # Create the data payload with required fields
    # These fields will be processed by the OpenMeter collector for pricing
    data = {
        "ai_duration_seconds": ai_duration_seconds,
        "listener_duration_seconds": listener_duration_seconds,
        "model": model,
        "language_pair": language_pair,
        "session_id": session_id,
    }
    if listener_id:
        data["listener_id"] = listener_id

    # Create CloudEvent following v1.0 specification
    # ID is auto-generated by CloudEvents library
    # Time is automatically set to now
    event = CloudEvent(
        attributes={
            "type": "session_usage",
            "source": "tiong",
            "subject": subject,
        },
        data=data,
    )

    # Convert to dict and send via HTTP
    event_dict = to_dict(event)

    async with httpx.AsyncClient() as client:
        response = await client.post(
            OM_COLLECTOR_ENDPOINT,
            json=event_dict,
            headers={"Content-Type": "application/cloudevents+json"},
        )
        response.raise_for_status()

    return True


async def query_meter_value(
    meter_slug: str,
    # TODO: update with Sep update see create_openmeter_customer
    subject: str,
    from_time_utc: Optional[datetime] = None,
    to_time_utc: Optional[datetime] = None,
    group_by: Optional[list[str]] = None,
    filter_group_by: Optional[dict[str, list[str]]] = None,
) -> dict:
    """Query aggregated meter values from OpenMeter API using POST endpoint

    This function queries OpenMeter using DAY window size for maximum aggregation,
    then collapses the time dimension by summing values across all windows for
    each unique combination of group_by fields.

    Args:
        meter_slug: The meter slug to query
        subject: Subject to filter by
            should be the openmeter_customer_key in the profiles table
        from_time_utc: Start time for query (must be UTC if provided).
            Use datetime.now(timezone.utc)
        to_time_utc: End time for query (must be UTC if provided).
            Use datetime.now(timezone.utc)
        group_by: List of fields to group results by (e.g., ["model", "session_id"])
            Returns separate results for each unique combination
        filter_group_by: Dict to filter by specific groupBy values
                        (e.g., {"model": ["Myna", "GPT-4"], "type": ["input"]})

    Returns:
        Dict with same structure as OpenMeter API but with time windows collapsed.
        Each data element contains the group_by fields plus a total aggregated "value".

    Ref: https://github.com/openmeterio/openmeter/blob/main/api/openapi.yaml
    """
    # Validate that datetime objects are UTC
    if from_time_utc and from_time_utc.tzinfo != timezone.utc:
        raise ValueError(
            "from_time_utc must be in UTC timezone. "
            "Use datetime.now(timezone.utc) to create UTC datetime."
        )
    if to_time_utc and to_time_utc.tzinfo != timezone.utc:
        raise ValueError(
            "to_time_utc must be in UTC timezone. "
            "Use datetime.now(timezone.utc) to create UTC datetime."
        )

    # Build JSON body for POST request
    body = {
        "subject": [subject],  # API expects array
        # Use DAY window size for maximum aggregation, then collapse windows client-side
        "windowSize": "DAY",
    }
    # Only add time constraints if provided, otherwise query all historical data
    if from_time_utc:
        body["from"] = from_time_utc.isoformat()
    if to_time_utc:
        body["to"] = to_time_utc.isoformat()
    # Add groupBy parameter if provided
    if group_by:
        body["groupBy"] = group_by
    # Add filterGroupBy parameter if provided
    if filter_group_by:
        body["filterGroupBy"] = filter_group_by

    # Use POST endpoint with JSON body
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{OM_BASE_URL}/api/v1/meters/{meter_slug}/query",
            json=body,
            headers={
                "Authorization": f"Bearer {OM_TOKEN}",
                "Content-Type": "application/json",
            },
        )
        response.raise_for_status()
        raw_data = response.json()

        # Collapse window dimension by aggregating values by group_by keys
        if "data" not in raw_data or not isinstance(raw_data["data"], list):
            raise ValueError("X OpenMeter meter api return structure not as expected")

        # Dictionary to accumulate totals by group_by tuple
        totals = {}
        for window in raw_data["data"]:
            if "value" not in window:
                continue

            # Create tuple key from group_by fields
            if group_by:
                key = tuple(window["groupBy"].get(field) for field in group_by)
            else:
                key = ()

            totals[key] = totals.get(key, 0) + float(window["value"])

        # Convert back to OpenMeter format: list of dicts with group_by fields + value
        collapsed_data = []
        for key, total_value in totals.items():
            data_point = {"value": total_value}

            # Add group_by fields to the data point
            if group_by:
                for i, field in enumerate(group_by):
                    data_point[field] = key[i] if i < len(key) else None

            collapsed_data.append(data_point)

        # Return with same structure but collapsed windows
        return collapsed_data


async def query_all_meters_for_session(openmeter_customer_key: str, session_id: str):
    # TODO: hardcoded meter names
    meters = [
        "ai_interpreter_seconds",
        "total_listeners_seconds",
        "total_cost",
        "listeners_count",
        "total_ai_interpreter_cost",
        "total_listeners_cost",
    ]

    # Create tasks for all meter queries
    tasks = [
        query_meter_value(
            meter_slug=meter_slug,
            subject=openmeter_customer_key,
            filter_group_by={"session_id": [session_id]},
            group_by=["language_pair"],
        )
        for meter_slug in meters
    ]

    # Execute all queries concurrently
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Build response with results from each meter
    meter_results = {}
    for i, meter_slug in enumerate(meters):
        result = results[i]

        meter_results[meter_slug] = {
            "group_by_language_pair": {
                entry["language_pair"]: entry["value"] for entry in result
            },
            "total": sum(entry["value"] for entry in result),
        }

    return meter_results


async def create_user_entitlement(
    customer_key: str, feature_key: str = INTERPRETER_CREDITS_FEATURE_KEY
) -> dict:
    """Create a metered entitlement for a user with zero initial balance.

    This function creates a credit-based entitlement that starts with zero balance
    and only provides credits when grants are added (after payment). This implements
    a pure pay-as-you-go model with no free usage. Entitlements are required to give
    user grants.

    Args:
        customer_key: The customer key from profiles.openmeter_customer_key
        feature_key: The feature key for the entitlement

    Returns:
        Dict containing the entitlement creation response from OpenMeter API.
                "id": "01J5PVD9...",
                "type": "metered",
                "subjectKey": "customer-key",
                "featureKey": "interpreter_credits",
                "config": {...}

    Ref: https://openmeter.io/docs/billing/entitlements/quickstart

    Note:
        - Creates entitlement with 0 initial balance (no free credits)
        - Uses 100-year usage period to simulate permanent credits
        - Sets hard limit (isSoftLimit=false) to block access when balance=0
        - API is idempotent, a customer can have one entitlement per featureKey
    """
    entitlement_data = {
        "type": "metered",
        "featureKey": feature_key,
        "issueAfterReset": 0,  # Zero initial balance - no free credits
        "usagePeriod": {
            "interval": "YEAR",
            "count": 100,  # 100 years = effectively permanent
        },
        "isSoftLimit": False,  # Hard limit - blocks access when balance = 0
    }

    async with httpx.AsyncClient() as client:
        # TODO: Update to use customer endpoint after September 2025,
        # check create_openmeter_customer docstring
        response = await client.post(
            f"{OM_BASE_URL}/api/v1/subjects/{customer_key}/entitlements",
            json=entitlement_data,
            headers={
                "Authorization": f"Bearer {OM_TOKEN}",
                "Content-Type": "application/json",
            },
        )
        response.raise_for_status()

        return response.json()


async def create_openmeter_grant(
    customer_key: str,
    feature_key: str = INTERPRETER_CREDITS_FEATURE_KEY,
    amount: float = 0.0,
    priority: int = 1,
) -> dict:
    """Create a grant to add credits to a user's entitlement after payment.

    Args:
        customer_key: The customer key from profiles.openmeter_customer_key
        feature_key: The feature key for the entitlement
        amount: The amount of credits to grant (e.g., 25.0 for $25)
        priority: Grant priority for burn-down order (lower = higher priority)

    Returns:
        Dict containing the grant creation response from OpenMeter API.
            "id": "01J5PVD9...",
            "amount": 25.0,
            "priority": 1,
            "effectiveAt": "2025-08-21T...",
            "expiration": {...}

    Ref: https://openmeter.io/docs/billing/entitlements/grant

    Note:
        - Credits expire after 100 years (effectively permanent)
        - effectiveAt is set to current time (immediate activation)
    """
    if amount <= 0:
        raise ValueError("Grant amount must be positive")

    grant_data = {
        "amount": amount,
        "priority": priority,
        "effectiveAt": datetime.now(timezone.utc).isoformat(),
        "expiration": {
            "duration": "YEAR",
            "count": 100,  # 100 years = effectively no expiry
        },
    }

    async with httpx.AsyncClient() as client:
        # TODO: Update to use customer endpoint after September 2025,
        # check create_openmeter_customer docstring
        response = await client.post(
            f"{OM_BASE_URL}/api/v1/subjects/{customer_key}/entitlements/{feature_key}/grants",
            json=grant_data,
            headers={
                "Authorization": f"Bearer {OM_TOKEN}",
                "Content-Type": "application/json",
            },
        )
        response.raise_for_status()

        return response.json()


async def create_openmeter_customer(
    user_id: str, email: str, name: Optional[str] = None
) -> dict:
    """Create a customer in OpenMeter using the user ID as both customer key and
    subject key.

    This function creates a customer in OpenMeter's billing system using the user's UUID
    as the customer key and subject key. subjects are the entity referenced when usage
    billing while customers are referenced in billing. Because of the OpenMeter upcoming
    update, model details below, both are created in preparation of the migration.

    Args:
        user_id: The user's UUID from the authentication system
        email: The user's email address
        name: Optional display name for the customer
            (defaults to email prefix if not provided)

    Returns:
        Dict containing the customer creation response from OpenMeter API.
        Example:
            {
            "createdAt": "2025-08-21T13:44:43.694067549Z",
            "id": "01K36DZTXESY0BYES64KZQ0N24",
            "key": "9bfe331c-bc05-443c-b0e2-7c7cd1d47ae0",
            "metadata": null,
            "name": "testuser",
            "primaryEmail": "<EMAIL>",
            "updatedAt": "2025-08-21T13:44:43.69406812Z",
            "usageAttribution": {
                "subjectKeys": [
                    "9bfe331c-bc05-443c-b0e2-7c7cd1d47ae0"
                ]
            }
        }

    TODO: OpenMeter Subject API Migration (September 2025)
    ====================================================
    OpenMeter is migrating from subjects to customers with this timeline:
    - September 1, 2025: Subject APIs (/api/v*/subjects) marked as deprecated
    - November 1, 2025: Subject APIs completely removed

    Current Implementation Strategy:
    - We use user_id as both customer.key and subject in usage events

    Migration Steps (Before November 1, 2025):
    1. since customer key and subject key are the same there is no changes needed to
        send_session_usage_event, query_meter_value, or this function
    2. `"usageAttribution": {"subjectKeys": [user_id]}` will become optional so it can
        be removed

    References:
    - Migration guide: https://openmeter.io/blog/updates-to-the-subject-apis#-key-changes
    - Key insight: "Events with a subject matching a Customer Key will automatically
      be attributed to that customer"
    """
    display_name = name or email.split("@")[0]

    customer_data = {
        "key": user_id,
        "name": display_name,
        "usageAttribution": {"subjectKeys": [user_id]},
        "primaryEmail": email,
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{OM_BASE_URL}/api/v1/customers",
            json=customer_data,
            headers={
                "Authorization": f"Bearer {OM_TOKEN}",
                "Content-Type": "application/json",
            },
        )
        response.raise_for_status()

        logger.info(f"Created OpenMeter customer for user {user_id}")
        return response.json()


async def check_user_entitlement(
    customer_key: str, feature_key: str = INTERPRETER_CREDITS_FEATURE_KEY
) -> dict:
    """Check the current entitlement value for a user's feature access.

    This function queries OpenMeter to get the current entitlement value for a specific
    feature, including access status, balance, usage, and overage information.

    Args:
        customer_key: The customer key from profiles.openmeter_customer_key
        feature_key: The feature key for the entitlement
            (defaults to INTERPRETER_CREDITS_FEATURE_KEY)

    Returns:
        Dict containing the entitlement value response from OpenMeter API.
        - hasAccess indicates if the user can currently access the feature
        - balance shows remaining credits available
        - usage shows total consumed credits in current period
        - overage shows credits consumed beyond the initial allocation

    Ref: https://openmeter.io/docs/api/cloud#tag/entitlements/get/api/v1/customers/{customerIdOrKey}/entitlements/{featureKey}/value
    """
    async with httpx.AsyncClient() as client:
        # TODO: Update to use customer endpoint after September 2025,
        # check create_openmeter_customer docstring
        response = await client.get(
            f"{OM_BASE_URL}/api/v1/subjects/{customer_key}/entitlements/{feature_key}/value",
            headers={
                "Authorization": f"Bearer {OM_TOKEN}",
                "Content-Type": "application/json",
            },
        )
        response.raise_for_status()

        return response.json()
