import asyncio
from datetime import datetime
from pathlib import Path

from fastapi import Depends, FastAPI, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from loguru import logger

from .auth import (
    verify_and_get_client_with_email,
)
from .billing import billing_router
from .broadcaster import broadcaster_router
from .interpreters import interpreters_router
from .listener import listener_router
from .openmeter import query_all_meters_for_session
from .profiles import get_or_create_profile


def asyncio_exception_handler(loop, context):
    """Global exception handler for asyncio tasks"""
    exception = context.get("exception")
    if exception:
        logger.exception(
            f"Uncaught asyncio exception in "
            f"{context.get('task', 'unknown task')}: {exception}",
            exception=exception,
        )
    else:
        logger.error(f"Asyncio error: {context['message']}")


app = FastAPI()

app.include_router(billing_router)
app.include_router(broadcaster_router)
app.include_router(listener_router)
app.include_router(interpreters_router)

# Get the directory where this script is located
BASE_DIR = Path(__file__).parent
STATIC_DIR = BASE_DIR / "static"

# Create static directory if it doesn't exist
STATIC_DIR.mkdir(exist_ok=True)

app.mount("/static", StaticFiles(directory=str(STATIC_DIR)), name="static")


@app.on_event("startup")
async def startup_event():
    """Initialize application on startup"""
    logger.info("Starting Tiong application...")

    # Set global asyncio exception handler
    loop = asyncio.get_event_loop()
    loop.set_exception_handler(asyncio_exception_handler)
    logger.info("Global asyncio exception handler configured")
    logger.success("Tiong application started successfully")


@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring"""
    return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}


@app.get("/", response_class=HTMLResponse)
def serve_landing_page():
    """Serve the main index page"""
    index_html_static = STATIC_DIR / "index.html"
    index_html_frontend = BASE_DIR.parent.parent.parent / "frontend" / "index.html"

    if index_html_static.exists():
        with open(index_html_static, "r") as f:
            return HTMLResponse(content=f.read())
    elif index_html_frontend.exists():
        with open(index_html_frontend, "r") as f:
            return HTMLResponse(content=f.read())
    else:
        raise HTTPException(status_code=500, detail="Landing page not found")


@app.get("/api/query-meter/{session_id}")
async def query_session_id_meter(
    session_id: str, auth_data=Depends(verify_and_get_client_with_email)
):
    try:
        user_id, client, email = auth_data

        # TODO: should not create profile here, should update the follow to detect that
        # the user does not have credits when he attempts to start a session
        profile = await get_or_create_profile(user_id, client, email)
        openmeter_customer_key = profile["openmeter_customer_key"]

        return await query_all_meters_for_session(openmeter_customer_key, session_id)

    except Exception as e:
        logger.error(f"Failed to query meters for session {session_id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to query meter data: {str(e)}"
        )
