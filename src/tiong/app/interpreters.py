import os
from enum import Enum

import asyncssh
from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException
from loguru import logger
from pydantic import BaseModel

from .auth import Client, verify_and_get_client
from .runpod import RunPodSetupError, add_pod, remove_pod

interpreters_router = APIRouter()


class Language(str, Enum):
    EN = "en"
    EN_US = "en-US"
    EN_GB = "en-GB"
    ES = "es"
    ES_ES = "es-ES"
    ES_US = "es-US"
    DE = "de"
    DE_DE = "de-DE"
    IT = "it"
    IT_IT = "it-IT"
    FR = "fr"
    FR_FR = "fr-FR"
    AR = "ar"
    HI = "hi"
    HI_IN = "hi-IN"
    ID = "id"
    PL = "pl"
    PL_PL = "pl-PL"
    PT = "pt"
    PT_PT = "pt-PT"
    PT_BR = "pt-BR"
    RU = "ru"
    RU_RU = "ru-RU"

    def to_target_config(self) -> str:
        """
        Maps this Language enum value to target language config filename for run-bot.sh.
        Returns the filename without .gin extension.
        """
        # Map enum values to actual config filenames available in
        # pipit/config/target_languages/
        language_mapping = {
            Language.EN_US: "en-US",
            Language.EN_GB: "en-GB",
            Language.ES_ES: "es-ES",
            Language.ES_US: "es-US",
            Language.DE_DE: "de-DE",
            Language.IT_IT: "it-IT",
            Language.FR_FR: "fr-FR",
            Language.HI_IN: "hi-IN",
            Language.PL_PL: "pl-PL",
            Language.PT_PT: "pt-PT",
            Language.PT_BR: "pt-BR",
            Language.RU_RU: "ru-RU",
            # Fallback mappings for generic language codes
            Language.EN: "en-US",  # Default English to US
            Language.ES: "es-ES",  # Default Spanish to Spain
            Language.DE: "de-DE",  # Default German to Germany
            Language.IT: "it-IT",  # Default Italian to Italy
            Language.FR: "fr-FR",  # Default French to France
            Language.AR: "ar-XA",  # Default Arabic to Modern Standard Arabic
            Language.HI: "hi-IN",  # Default Hindi to India
            Language.ID: "id-ID",  # Default Indonesian to Indonesia
            Language.PL: "pl-PL",  # Default Polish to Poland
            Language.PT: "pt-PT",  # Default Portuguese to Portugal
            Language.RU: "ru-RU",  # Default Russian to Russia
        }

        if self in language_mapping:
            return language_mapping[self]

        raise ValueError(f"Language '{self.value}' is not supported. ")


class AddInterpreterRequest(BaseModel):
    source_language: Language
    target_language: Language
    livekit_room_id: str


class AddInterpreterResponse(BaseModel):
    stdout: str
    stderr: str
    exit_code: int
    success: bool


SSH_KEY_ID = os.getenv("SSH_KEY_ID", None)
if not SSH_KEY_ID:
    error_msg = "SSH_KEY_ID environment variable is required."
    logger.error(error_msg)
    raise ValueError(error_msg)


async def start_interpreter_session(
    ssh_host: str,
    ssh_port: int,
    request: AddInterpreterRequest,
    interpreter_id: str,
    user_data: tuple[str, Client],
) -> str:
    """
    SSH into the assigned server, execute the run-bot command, and "
    "return the tmux session ID. Updates the interpreter status and "
    tmux_session_id in the database.
    """

    target_config = request.target_language.to_target_config()
    conn_params = {
        "host": ssh_host,
        "port": ssh_port,
        "username": "root",
        "client_keys": [SSH_KEY_ID],
        "known_hosts": None,  # Skip host key verification (use cautiously)
        "connect_timeout": 30,
    }

    async with asyncssh.connect(**conn_params) as ssh_conn:
        # Use the mapped target language config in the run-bot command
        run_bot_cmd = (
            f"exec ./run_bot.sh {request.livekit_room_id} {request.source_language} "
            f"{target_config}"
        )

        logger.info(f"Executing command on {ssh_host}:{ssh_port}: {run_bot_cmd}")
        result = await ssh_conn.run(run_bot_cmd, check=True, timeout=60)
        tmux_session_id = result.stdout.strip()

        if not tmux_session_id:
            stderr_output = result.stderr or "No error output"
            raise Exception(
                f"run-bot command did not return a tmux session ID. "
                f"stderr: {stderr_output}"
            )

        # Update the interpreter with tmux session ID and mark as STARTING
        user_id, user_client = user_data
        result = user_client.table("ai_interpreters")
        result = result.update(
            {"tmux_session_id": tmux_session_id, "status": "STARTING"}
        )
        result = result.eq("uuid", interpreter_id)
        result = result.execute()
        logger.info(
            f"Successfully started interpreter {interpreter_id} "
            f"with tmux session '{tmux_session_id}' on server "
            f"{ssh_host}:{ssh_port} "
            f"(source: {request.source_language}, target: {target_config})"
        )
        return tmux_session_id


async def stop_interpreter_session(
    ssh_hostname: str, ssh_port: int, tmux_session_id: str
):
    """Stop a tmux session on a specific server"""
    conn_params = {
        "host": ssh_hostname,
        "port": ssh_port,
        "username": "root",
        "client_keys": [SSH_KEY_ID],
        "known_hosts": None,
        "connect_timeout": 30,
    }

    async with asyncssh.connect(**conn_params) as ssh_conn:
        # Kill the tmux session - properly escape the session ID to handle
        # special characters
        kill_cmd = f"tmux kill-session -t {tmux_session_id}"
        logger.info(f"Executing on {ssh_hostname}:{ssh_port}: {kill_cmd}")

        result = await ssh_conn.run(kill_cmd, timeout=30)

        if result.exit_status != 0:
            # Session might already be dead, which is fine
            logger.warning(
                f"tmux kill-session returned exit code {result.exit_status}. "
                f"Session may already be stopped."
            )
            logger.debug(f"stdout: {result.stdout}")
            logger.debug(f"stderr: {result.stderr}")

        logger.info(
            f"Successfully stopped tmux session {tmux_session_id} on "
            f"{ssh_hostname}:{ssh_port}"
        )


async def provision_interpreter_background(
    request: AddInterpreterRequest, interpreter_id: str, user_data: tuple[str, Client]
):
    user_id, user_client = user_data
    instance = None
    try:
        instance = await add_pod(user_data)

        # Update the interpreter with server_id
        result = user_client.table("ai_interpreters")
        result = result.update(
            {"server_id": instance.server_id, "status": "SERVER_ASSIGNED"}
        )
        result = result.eq("uuid", interpreter_id)
        result = result.eq("user_id", user_id)
        result = result.execute()

        if result.data:
            logger.info(f"Successfully updated interpreter {interpreter_id}")
        else:
            raise Exception(
                f"Failed to update server_id for interpreter {interpreter_id}"
            )

        logger.info(
            f"Successfully provisioned machine for interpreter {interpreter_id} "
            f" server id {instance.server_id}"
        )

        await start_interpreter_session(
            instance.ssh_host,
            instance.ssh_port,
            request,
            interpreter_id,
            user_data,
        )

    except RunPodSetupError as e:
        logger.error(f"Setup failed: {str(e)}")
        instance = e.instance

        # Update interpreter status to failed.
        result = (
            user_client.table("ai_interpreters")
            .update({"status": "FAILED", "error": str(e)})
            .eq("uuid", interpreter_id)
            .execute()
        )
        if result.data:
            logger.info(
                f"Successfully set the ai interpreter {interpreter_id} status to FAILED"
            )
            logger.info(f"Instance: {instance}")
        else:
            logger.warning(f"Updating ai interpreter {interpreter_id} status failed.")

        if instance and instance.pod_id:
            logger.info(f"Removing pod {instance.pod_id}")
            await remove_pod(user_data, instance.pod_id)
        raise


@interpreters_router.post("/api/add-interpreter/{session_id}")
async def broadcaster_add_interpreter(
    request: AddInterpreterRequest,
    session_id: str,
    background_tasks: BackgroundTasks,
    user_data: tuple[str, Client] = Depends(verify_and_get_client),
):
    user_id, user_client = user_data

    # Create the interpreter record first to get the ID
    interpreter_entry = {
        "session_id": session_id,
        "source_language": request.source_language,
        "target_language": request.target_language,
        "status": "PENDING",
        "server_id": None,  # Will be updated in background task
        "user_id": user_id,
    }

    result = user_client.table("ai_interpreters").insert(interpreter_entry).execute()

    if not result.data:
        raise HTTPException("Failed to add interpreter entry")

    interpreter_id = result.data[0]["uuid"]
    # Start the background provisioning process
    background_tasks.add_task(
        provision_interpreter_background, request, interpreter_id, user_data
    )

    return {
        "type": "add_interpreter_status",
        "interpreter_id": interpreter_id,
        "status": "processing request..",
    }


async def delete_interpreter(
    user_data: tuple[str, Client],
    interpreter_id: str,
) -> bool:
    user_id, user_client = user_data

    response = user_client.table("ai_interpreters")
    response = response.select(
        "uuid, server_id, tmux_session_id, servers(ssh_host, ssh_port, runpod_id)"
    )
    response = response.eq("uuid", interpreter_id)
    response = response.execute()

    interpreter = response.data[0]
    if not interpreter:
        logger.warning(f"No interpreters {interpreter_id} found")
        return False

    logger.debug(f"Interpreters: {interpreter}")

    server_id = interpreter["server_id"]
    tmux_session_id = interpreter["tmux_session_id"]
    ssh_host = interpreter["servers"]["ssh_host"]
    ssh_port = interpreter["servers"]["ssh_port"]
    pod_id = interpreter["servers"]["runpod_id"]
    current_uuid = interpreter["uuid"]

    logger.info(f"Deleting interpreter {current_uuid}")

    # If interpreter has a server and tmux session, stop it
    if server_id and tmux_session_id and ssh_host and ssh_port:
        await stop_interpreter_session(ssh_host, ssh_port, tmux_session_id)
        logger.info(
            f"Successfully stopped tmux session {tmux_session_id} on server {server_id}"
        )

    # Delete the interpreter
    response = user_client.table("ai_interpreters").delete()
    response = response.eq("uuid", interpreter_id)
    response.execute()

    await remove_pod(user_data, pod_id)
    return True


@interpreters_router.delete("/api/delete-interpreter/{interpreter_id}")
async def broadcaster_delete_interpreter(
    interpreter_id: str,
    user_data: tuple[str, Client] = Depends(verify_and_get_client),
):
    """Delete an interpreter and stop its session"""
    await delete_interpreter(user_data, interpreter_id)

    return {
        "status": "success",
        "message": "Interpreter successfully stopped and removed",
    }
