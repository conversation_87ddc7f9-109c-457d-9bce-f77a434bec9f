"""
Module to manage runpod instances where inference processes are run.
"""

import asyncio
import os
import platform
import re
import secrets
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import List, Tuple

from loguru import logger

from .auth import Client


class GPUType(Enum):
    RTX_5090 = "NVIDIA GeForce RTX 5090"
    RTX_4090 = "NVIDIA GeForce RTX 4090"


class RunPodStatus(Enum):
    CREATING = "creating"
    RUNNING = "running"
    READY = "ready"
    ERROR = "error"
    TERMINATED = "terminated"


@dataclass
class RunPodConfig:
    """Configuration for RunPod instance creation"""

    gpu_type: GPUType = GPUType.RTX_5090
    gpu_count: int = 1
    container_disk_size_gb: int = 100
    image_name: str = "runpod/pytorch:2.8.0-py3.11-cuda12.8.1-cudnn-devel-ubuntu22.04"
    template_id: str = "runpod-torch-v21"
    ssh_key_path: str = "~/.ssh/id_ed25519_voicefrom"
    timeout_seconds: int = 120
    check_interval: int = 10


@dataclass
class RunPodInstance:
    """Represents a RunPod instance"""

    pod_id: str
    pod_name: str
    ssh_host: str | None = None
    ssh_port: int | None = None
    server_id: str | None = None
    status: RunPodStatus = RunPodStatus.CREATING
    capacity: int = 1


class RunPodSetupError(Exception):
    """Custom exception for RunPod setup errors"""

    def __init__(
        self,
        message: str,
        instance: RunPodInstance | None = None,
        error_code: str | None = None,
    ):
        super().__init__(message)
        self.message = message
        self.instance = instance
        self.error_code = error_code

    def __str__(self):
        base_msg = self.message
        if self.instance:
            base_msg += f" (Pod: {self.instance.pod_name}, ID: {self.instance.pod_id})"
        if self.error_code:
            base_msg += f" [Error: {self.error_code}]"
        return base_msg


RUNPOD_API_KEY = os.getenv("RUNPOD_API_KEY")
GH_TOKEN = os.getenv("GH_TOKEN")
HF_TOKEN = os.getenv("HF_TOKEN")

if not RUNPOD_API_KEY:
    raise RunPodSetupError(
        "RUNPOD_API_KEY must be provided or set as environment variable"
    )
if not GH_TOKEN:
    raise RunPodSetupError("GH_TOKEN must be provided or set as environment variable")
if not HF_TOKEN:
    raise RunPodSetupError("HF_TOKEN must be provided or set as environment variable")

system = platform.system().lower()
machine = platform.machine().lower()

if system == "darwin" and "arm" in machine:
    RUNPODCTL_PATH = "./tools/runpodctl-darwin-arm64"
elif system == "linux" and "x86_64" in machine:
    RUNPODCTL_PATH = "./tools/runpodctl-linux-amd64"
else:
    raise RunPodSetupError(f"Unsupported platform: {system}-{machine}")

TTS_CREDS_PATH = Path("secrets/tts-credentials.json")
if not TTS_CREDS_PATH.exists():
    raise RunPodSetupError("Missing critical tts credentials.")


PIPIT_DOT_ENV = Path("secrets/pipit-dot-env")
if not PIPIT_DOT_ENV.exists():
    raise RunPodSetupError("Missing pipit dot env file.")


def generate_pod_name() -> str:
    """Generate a random pod name"""
    return f"pod-{secrets.token_hex(6)}"


class RunCommandError(Exception):
    def __init__(
        self,
        message: str,
        stderr: str | None,
    ):
        super().__init__(message)
        self.message = message
        self.stderr = stderr

    def __str__(self):
        base_msg = self.message
        if self.stderr:
            base_msg += f" (Stderr: {self.stderr})"
        return base_msg


async def run_command(command: List[str], timeout: int = 30) -> Tuple[str, str, int]:
    """Run a command asynchronously and return the output stdout if successful."""
    process = await asyncio.create_subprocess_exec(
        *command,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE,
    )

    stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=timeout)

    if process.returncode != 0:
        raise RunCommandError("Command failed.", stderr.decode())

    return stdout.decode()


async def create_pod(config: RunPodConfig) -> RunPodInstance:
    """Create a new RunPod instance"""
    pod_name = generate_pod_name()

    logger.info(f"Creating RunPod instance: {pod_name}")

    command = [
        RUNPODCTL_PATH,
        "create",
        "pod",
        "--startSSH",
        "--secureCloud",
        "--gpuType",
        config.gpu_type.value,
        "--gpuCount",
        str(config.gpu_count),
        "--templateId",
        config.template_id,
        "--imageName",
        config.image_name,
        "--containerDiskSize",
        str(config.container_disk_size_gb),
        "--name",
        pod_name,
    ]

    stdout = await run_command(command, timeout=60)

    pod_id_match = re.search(r'"([^"]*)"', stdout)
    if not pod_id_match:
        raise RunPodSetupError(f"Could not extract pod ID from output: {stdout}")

    pod_id = pod_id_match.group(1)
    instance = RunPodInstance(
        pod_id=pod_id, pod_name=pod_name, status=RunPodStatus.CREATING
    )

    logger.info(f"Pod created with ID: {pod_id}")
    return instance


async def wait_for_pod_ready(
    instance: RunPodInstance, config: RunPodConfig
) -> RunPodInstance:
    """Wait for pod to be ready and extract SSH connection details"""
    logger.info(f"Waiting for instance {instance.pod_name} to be ready...")

    elapsed = 0

    while elapsed < config.timeout_seconds:
        try:
            stdout = await run_command(
                [RUNPODCTL_PATH, "ssh", "connect", instance.pod_name],
            )
        except RunCommandError:
            logger.info(
                f"Instance not ready yet, waiting {config.check_interval}s... "
                f"({elapsed}s elapsed)"
            )
            await asyncio.sleep(config.check_interval)
            elapsed += config.check_interval
            continue

        if stdout.strip().startswith("ssh"):
            # Parse SSH connection details
            ssh_cmd = stdout.strip()

            host_match = re.search(r"([0-9]+\.[0-9]+\.[0-9]+\.[0-9]+)", ssh_cmd)
            port_match = re.search(r"-p (\d+)", ssh_cmd)

            if host_match and port_match:
                instance.ssh_host = host_match.group(1)
                instance.ssh_port = int(port_match.group(1))
                instance.status = RunPodStatus.READY

                logger.info(
                    f"Instance ready! SSH: {instance.ssh_host}:{instance.ssh_port}"
                )
                return instance
            else:
                raise RunPodSetupError(
                    f"Could not parse SSH details from: {ssh_cmd}", instance=instance
                )

    instance.status = RunPodStatus.ERROR
    raise RunPodSetupError(
        f"Timeout: Instance not ready after {config.timeout_seconds}s",
        instance=instance,
    )


async def scp_file(local_path: Path, remote_path: str, port: int, ssh_key: str):
    """Copy file via SCP"""
    command = [
        "scp",
        "-o",
        "StrictHostKeyChecking=no",
        "-P",
        str(port),
        "-i",
        ssh_key,
        str(local_path),
        remote_path,
    ]

    await run_command(command, timeout=60)


async def ssh_command(host: str, port: int, ssh_key: str, command: str) -> str:
    """Run command via SSH"""
    ssh_command_list = [
        "ssh",
        "-o",
        "StrictHostKeyChecking=no",
        "-p",
        str(port),
        "-i",
        ssh_key,
        f"root@{host}",
        command,
    ]

    return await run_command(ssh_command_list, timeout=300)


async def copy_secrets(instance: RunPodInstance, config: RunPodConfig):
    """Copy secret files to the instance"""
    ssh_key_path = os.path.expanduser(config.ssh_key_path)

    # Copy TTS credentials if exists
    await scp_file(
        TTS_CREDS_PATH,
        f"root@{instance.ssh_host}:~/tts-credentials.json",
        instance.ssh_port,
        ssh_key_path,
    )

    # Copy pipit .env if exists
    await scp_file(
        PIPIT_DOT_ENV,
        f"root@{instance.ssh_host}:~/pipit/.env",
        instance.ssh_port,
        ssh_key_path,
    )


async def bootstrap_machine(instance: RunPodInstance, config: RunPodConfig) -> None:
    """Bootstrap the machine with required software"""
    if not instance.ssh_host or not instance.ssh_port:
        raise RunPodSetupError("Instance must be ready before bootstrapping", instance)

    logger.info("Bootstrapping machine...")

    ssh_key_path = os.path.expanduser(config.ssh_key_path)

    try:
        # Copy scripts to instance
        await scp_file(
            Path("./scripts") / "setup.sh",
            f"root@{instance.ssh_host}:~/setup.sh",
            instance.ssh_port,
            ssh_key_path,
        )
        await scp_file(
            Path("./scripts") / "run_bot.sh",
            f"root@{instance.ssh_host}:~/run_bot.sh",
            instance.ssh_port,
            ssh_key_path,
        )

        # Run setup script
        await ssh_command(
            instance.ssh_host,
            instance.ssh_port,
            ssh_key_path,
            (
                f"chmod +x setup.sh && export GH_TOKEN={GH_TOKEN} && "
                f"export HF_TOKEN={HF_TOKEN} && ./setup.sh"
            ),
        )

        # Copy secrets if they exist
        await copy_secrets(instance, config)
        logger.info("Machine bootstrap completed")

    except RunCommandError as e:
        raise RunPodSetupError(e.message, instance=instance)


async def add_to_database(
    instance: RunPodInstance, user_data: tuple[str, Client]
) -> RunPodInstance:
    """Add instance to Supabase database"""
    user_id, user_client = user_data
    instance_data = {
        "ssh_host": instance.ssh_host,
        "ssh_port": instance.ssh_port,
        "capacity": instance.capacity,
        "runpod_id": instance.pod_id,
        "user_id": user_id,
    }

    result = user_client.table("servers").insert(instance_data).execute()

    if not result.data:
        raise RunPodSetupError("Failed to add instance to database", instance=instance)

    logger.info(f"Instance added to database with ID: {result.data[0]['id']}")
    instance.server_id = result.data[0]["id"]
    return instance


async def add_pod(
    user_data: tuple[str, Client], config: RunPodConfig = RunPodConfig()
) -> RunPodInstance:
    """Complete setup process: create, bootstrap, and register instance"""
    user_id, user_client = user_data

    instance = await create_pod(config)
    instance = await wait_for_pod_ready(instance, config)
    await bootstrap_machine(instance, config)
    instance = await add_to_database(instance, user_data)
    return instance


async def remove_pod(user_data: tuple[str, Client], pod_id: str) -> None:
    """Remove a RunPod instance"""
    user_id, user_client = user_data
    result = user_client.table("servers").delete().eq("runpod_id", pod_id).execute()

    if result.data:
        logger.info(f"Successfully deleted runpod_id {pod_id} from servers table.")
    else:
        logger.warning(f"Deletion of runpod_id {pod_id} from servers failed.")

    command = [RUNPODCTL_PATH, "remove", "pod", pod_id]
    await run_command(command)

    logger.info(f"Pod {pod_id} removed successfully")
