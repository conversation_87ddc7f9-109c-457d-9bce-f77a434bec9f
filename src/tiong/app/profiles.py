"""
Profile management functions for user profiles and Stripe customer integration.
"""

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from loguru import logger

from supabase import Client

from .auth import get_service_supabase_client
from .openmeter import create_openmeter_customer, create_user_entitlement
from .stripe import create_stripe_customer


async def get_or_create_profile(
    user_id: str, user_client: Client, user_email: str
) -> dict:
    """
    Get or create user profile with both Stripe and OpenMeter customer setup.

    This function ensures the user has a complete profile with both Stripe customer ID
    and OpenMeter subject ID. It creates missing customers as needed and updates the
    profile table accordingly.

    Args:
        user_id: The authenticated user's UUID from JWT token
        user_client: Authenticated Supabase client with RLS enabled
        user_email: User's email address from JWT token payload

    Returns:
        Dict containing both customer IDs:
            "stripe_customer_id"
            "openmeter_customer_key"
    """
    try:
        # Check existing profile data using user client (respects RLS)
        response = (
            user_client.table("profiles")
            .select("stripe_customer_id, openmeter_customer_key")
            .execute()
        )

        profile_data = response.data[0] if response.data else {}
        stripe_customer_id = profile_data.get("stripe_customer_id")
        openmeter_customer_key = profile_data.get("openmeter_customer_key")

        updates = {}

        # Create Stripe customer if needed
        if not stripe_customer_id:
            stripe_customer = create_stripe_customer(
                email=user_email, metadata={"user_id": user_id}
            )
            stripe_customer_id = stripe_customer.id
            updates["stripe_customer_id"] = stripe_customer_id
            logger.success(f"Created Stripe customer {stripe_customer_id}")

        # Create OpenMeter customer if needed
        if not openmeter_customer_key:
            await create_openmeter_customer(
                user_id=user_id, email=user_email, name=user_email.split("@")[0]
            )
            openmeter_customer_key = user_id  # Use user_id as customer key

            # Create entitlement with zero initial balance for credit-based billing
            await create_user_entitlement(customer_key=openmeter_customer_key)

            updates["openmeter_customer_key"] = openmeter_customer_key
            logger.success(
                "Created OpenMeter customer and entitlement for"
                f" customer key {openmeter_customer_key}"
            )

        if updates:
            service_client = get_service_supabase_client()
            service_client.table("profiles").update(updates).eq("id", user_id).execute()

        return {
            "stripe_customer_id": stripe_customer_id,
            "openmeter_customer_key": openmeter_customer_key,
        }

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to setup user profile: {str(e)}"
        )


def get_profile_ids(user_client: Client) -> dict[str, str]:
    """Get OpenMeter and Stripe customer keys - only use when profile is guaranteed
    to exist."""
    result = (
        user_client.table("profiles")
        .select("openmeter_customer_key, stripe_customer_id")
        .execute()
    )
    if not result.data:
        raise ValueError("User profile not found")

    profile = result.data[0]
    return {
        "openmeter_customer_key": profile["openmeter_customer_key"],
        "stripe_customer_id": profile["stripe_customer_id"],
    }
