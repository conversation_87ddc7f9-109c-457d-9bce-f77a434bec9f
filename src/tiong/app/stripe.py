from os import environ
from typing import Any, Dict, Optional

import stripe

# Set your API key once at startup
stripe.api_key = environ.get("STRIPE_SECRET_KEY")
if not environ.get("STRIPE_SECRET_KEY"):
    raise ValueError("STRIPE_SECRET_KEY environment variable is required")


def create_stripe_customer(
    email: str, name: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None
) -> stripe.Customer:
    # Validate email
    if not email or not email.strip():
        raise ValueError("Email is required and cannot be empty")

    params = {
        "email": email.strip(),
        "metadata": metadata or {"service": "tiong"},
    }

    if name:
        params["name"] = name.strip()

    return stripe.Customer.create(**params)


def create_payment_intent(
    amount_usd: float,
    stripe_customer_id: str,
    metadata: Optional[Dict[str, Any]] = None,
) -> stripe.PaymentIntent:
    # Validate minimum amount (Stripe requirement: $0.50 USD minimum)
    if amount_usd < 0.50:
        raise ValueError("Amount must be at least $0.50")

    params = {
        "amount": int(amount_usd * 100),  # Convert dollars to cents
        "currency": "usd",
        "customer": stripe_customer_id,
        "setup_future_usage": "off_session",  # Save for future credit purchases
        "metadata": metadata or {"service": "tiong"},
    }

    return stripe.PaymentIntent.create(**params)


def get_customer_completed_payments(stripe_customer_id: str):
    payments = stripe.PaymentIntent.list(customer=stripe_customer_id)

    payment_data = []
    for payment in payments.data:
        # possible values: requires_payment_method, requires_confirmation,
        # requires_action, processing, requires_capture, canceled, or succeeded.
        if payment.status in ["succeeded", "processing", "canceled"]:
            payment_info = {
                "id": payment.id,
                "amount_usd": payment.amount / 100,
                "status": payment.status.upper(),
                "created": payment.created,
                "metadata": payment.metadata or {},
            }
            payment_data.append(payment_info)

    return payment_data
