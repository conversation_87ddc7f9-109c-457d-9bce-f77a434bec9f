"""Main entry point for the Tiong server"""

from pathlib import Path

import fire
import uvicorn
from dotenv import load_dotenv
from loguru import logger


def run_server(prod=False):
    """Run the Tiong server

    Args:
        prod (bool): Run in production mode (no auto-reload).
        Default: False (development mode)
    """
    # load environment variables
    load_dotenv()

    # Create logs directory
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # Configure loguru logging
    logger.remove()  # Remove default handler

    # Add console handler with colors
    logger.add(
        sink=lambda msg: print(msg, end=""),
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> "
        "| <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> "
        "- <level>{message}</level>",
        level="INFO",
        colorize=True,
    )

    # Add file handler for main application logs
    logger.add(
        sink="logs/tiong.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} "
        "- {message}",
        level="DEBUG",
        rotation="10 MB",
        retention=5,
        compression="zip",
    )

    # Add separate file handler for access logs
    logger.add(
        sink="logs/access.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | "
        "{name}:{function}:{line} - {message}",
        level="INFO",
        rotation="10 MB",
        retention=5,
        compression="zip",
        filter=lambda record: "uvicorn.access" in record["name"],
    )

    # Configure uvicorn to use minimal logging config since we're handling it
    # with loguru
    log_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": "%(message)s",
            },
        },
        "handlers": {
            "default": {
                "formatter": "default",
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stdout",
            },
        },
        "loggers": {
            "uvicorn": {
                "level": "INFO",
                "handlers": ["default"],
            },
            "uvicorn.access": {
                "level": "INFO",
                "handlers": ["default"],
                "propagate": False,
            },
        },
        "root": {
            "level": "INFO",
            "handlers": ["default"],
        },
    }

    mode_text = "PRODUCTION" if prod else "DEVELOPMENT"
    logger.info(f"Starting Tiong in {mode_text} mode")
    uvicorn.run(
        "tiong.app.main:app",
        host="",  # IPV6 and IPV4.
        port=8000,
        reload=not prod,
        log_config=log_config,
    )


def main():
    """Entry point for tiong command"""
    # Use Fire to handle command line arguments
    fire.Fire(run_server)
