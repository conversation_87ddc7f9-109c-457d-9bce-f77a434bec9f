-- Create pipit_cluster table
create table public.pipit_cluster (
  id bigint generated by default as identity not null,
  created_at timestamp with time zone not null default now(),
  ssh_hostname text not null,
  ssh_port smallint not null,
  runpod_id text null,
  is_busy boolean null,
  room_id text null,
  last_used_at timestamp without time zone null,
  constraint pipit_cluster_pkey primary key (id),
  constraint pipit_cluster_room_id_fkey foreign key (room_id) references room_info(room_name) on delete set null
) tablespace pg_default;
