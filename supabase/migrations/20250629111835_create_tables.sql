-- Create room_info table
create table public.room_info (
  id serial not null,
  room_name text not null,
  title text not null,
  description text null,
  created_at timestamp without time zone not null default now(),
  updated_at timestamp without time zone not null default now(),
  expires_at timestamp without time zone not null default (now() + interval '24 hours'),
  max_listeners integer null default 2,
  creator_email text null,
  constraint room_info_pkey primary key (room_name, id),
  constraint room_info_room_name_key unique (room_name)
) TABLESPACE pg_default;

-- Create tokens table with foreign key reference to room_info
create table public.tokens (
  id serial not null,
  token_value text not null,
  assigned_cookie text null,
  last_used_time timestamp without time zone null,
  created_at timestamp without time zone null default now(),
  room_name text null,
  expires_at timestamp without time zone null,
  constraint tokens_pkey primary key (id),
  constraint tokens_token_value_key unique (token_value),
  constraint tokens_room_name_fkey foreign key (room_name) references public.room_info (room_name) on delete cascade
) TABLESPACE pg_default;
