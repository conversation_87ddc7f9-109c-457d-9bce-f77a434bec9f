-- Add session title column and update session_room_view to include it and user_id

-- 1) Add column to sessions
alter table "public"."sessions"
    add column if not exists "title" text;

-- 2) Update the view to include session_title from sessions.title
create or replace view "public"."session_room_view" as
select
    s.id as session_id,
    s.created_at as session_created_at,
    s.livekit_room_id,
    s.status as session_status,
    r.url_identifier,
    r.title as room_title,
    r.description as room_description,
    r.status as room_status,
    s.title as session_title,
    s.user_id as user_id
from sessions s
join room_info r on s.room_id = r.id;
