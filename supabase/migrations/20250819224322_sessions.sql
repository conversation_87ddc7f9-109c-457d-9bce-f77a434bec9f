alter table "public"."ai_interpreters" drop constraint "ai_interpreters_room_name_fkey";

alter table "public"."tokens" drop constraint "tokens_room_name_fkey";

alter table "public"."room_info" drop constraint "room_info_room_name_key";

alter table "public"."room_info" drop constraint "room_info_pkey";

drop index if exists "public"."idx_tokens_room_name";

drop index if exists "public"."room_info_pkey";

drop index if exists "public"."room_info_room_name_key";

CREATE TYPE public.session_status AS ENUM ('CREATED', 'COMPLETED');

create table "public"."sessions" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default now(),
    "livekit_room_id" text,
    "livekit_broadcaster_token" text,
    "room_id" integer not null,
    "user_id" uuid,
    "status" public.session_status null
);


alter table "public"."sessions" enable row level security;

alter table "public"."ai_interpreters" drop column "room_name";

alter table "public"."ai_interpreters" add column "session_id" uuid;

alter table "public"."ai_interpreters" add column "user_id" uuid;

alter table "public"."room_info" drop column "created_at";

alter table "public"."room_info" drop column "creator_email";

alter table "public"."room_info" drop column "expires_at";

alter table "public"."room_info" drop column "max_listeners";

alter table "public"."room_info" drop column "room_name";

alter table "public"."room_info" drop column "updated_at";

alter table "public"."room_info" add column "url_identifier" text not null;

alter table "public"."room_info" add column "user_id" uuid;

alter table "public"."servers" drop column "ssh_hostname";

alter table "public"."servers" add column "ssh_host" text not null;

alter table "public"."servers" add column "user_id" uuid;

alter table "public"."servers" alter column "ssh_port" set data type integer using "ssh_port"::integer;

alter table "public"."tokens" drop column "room_name";

alter table "public"."tokens" add column "session_id" uuid;

CREATE UNIQUE INDEX sessions_livekit_room_name_key ON public.sessions USING btree (livekit_room_id);

CREATE UNIQUE INDEX sessions_pkey ON public.sessions USING btree (id);

CREATE UNIQUE INDEX room_info_pkey ON public.room_info USING btree (id);

CREATE UNIQUE INDEX room_info_room_name_key ON public.room_info USING btree (url_identifier);

alter table "public"."sessions" add constraint "sessions_pkey" PRIMARY KEY using index "sessions_pkey";

alter table "public"."room_info" add constraint "room_info_pkey" PRIMARY KEY using index "room_info_pkey";

alter table "public"."ai_interpreters" add constraint "ai_interpreters_session_id_fkey" FOREIGN KEY (session_id) REFERENCES sessions(id) not valid;

alter table "public"."ai_interpreters" validate constraint "ai_interpreters_session_id_fkey";

alter table "public"."ai_interpreters" add constraint "ai_interpreters_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."ai_interpreters" validate constraint "ai_interpreters_user_id_fkey";

alter table "public"."room_info" add constraint "room_info_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."room_info" validate constraint "room_info_user_id_fkey";

alter table "public"."servers" add constraint "servers_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."servers" validate constraint "servers_user_id_fkey";

alter table "public"."sessions" add constraint "sessions_livekit_room_name_key" UNIQUE using index "sessions_livekit_room_name_key";

alter table "public"."sessions" add constraint "sessions_room_id_fkey" FOREIGN KEY (room_id) REFERENCES room_info(id) not valid;

alter table "public"."sessions" validate constraint "sessions_room_id_fkey";

alter table "public"."sessions" add constraint "sessions_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."sessions" validate constraint "sessions_user_id_fkey";

alter table "public"."tokens" add constraint "tokens_session_id_fkey" FOREIGN KEY (session_id) REFERENCES sessions(id) not valid;

alter table "public"."tokens" validate constraint "tokens_session_id_fkey";

alter table "public"."room_info" add constraint "room_info_room_name_key" UNIQUE using index "room_info_room_name_key";

grant delete on table "public"."sessions" to "anon";

grant insert on table "public"."sessions" to "anon";

grant references on table "public"."sessions" to "anon";

grant select on table "public"."sessions" to "anon";

grant trigger on table "public"."sessions" to "anon";

grant truncate on table "public"."sessions" to "anon";

grant update on table "public"."sessions" to "anon";

grant delete on table "public"."sessions" to "authenticated";

grant insert on table "public"."sessions" to "authenticated";

grant references on table "public"."sessions" to "authenticated";

grant select on table "public"."sessions" to "authenticated";

grant trigger on table "public"."sessions" to "authenticated";

grant truncate on table "public"."sessions" to "authenticated";

grant update on table "public"."sessions" to "authenticated";

grant delete on table "public"."sessions" to "service_role";

grant insert on table "public"."sessions" to "service_role";

grant references on table "public"."sessions" to "service_role";

grant select on table "public"."sessions" to "service_role";

grant trigger on table "public"."sessions" to "service_role";

grant truncate on table "public"."sessions" to "service_role";

grant update on table "public"."sessions" to "service_role";


