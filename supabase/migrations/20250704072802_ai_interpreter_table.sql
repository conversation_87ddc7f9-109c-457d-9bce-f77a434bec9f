create type "public"."ai_interpreter_status" as enum ('SERVER_ASSIGNED', 'STARTING', 'RUNNING', 'STOPPED');

drop policy "Enable insert for authenticated users only" on "public"."pipit_cluster";

revoke delete on table "public"."pipit_cluster" from "anon";

revoke insert on table "public"."pipit_cluster" from "anon";

revoke references on table "public"."pipit_cluster" from "anon";

revoke select on table "public"."pipit_cluster" from "anon";

revoke trigger on table "public"."pipit_cluster" from "anon";

revoke truncate on table "public"."pipit_cluster" from "anon";

revoke update on table "public"."pipit_cluster" from "anon";

revoke delete on table "public"."pipit_cluster" from "authenticated";

revoke insert on table "public"."pipit_cluster" from "authenticated";

revoke references on table "public"."pipit_cluster" from "authenticated";

revoke select on table "public"."pipit_cluster" from "authenticated";

revoke trigger on table "public"."pipit_cluster" from "authenticated";

revoke truncate on table "public"."pipit_cluster" from "authenticated";

revoke update on table "public"."pipit_cluster" from "authenticated";

revoke delete on table "public"."pipit_cluster" from "service_role";

revoke insert on table "public"."pipit_cluster" from "service_role";

revoke references on table "public"."pipit_cluster" from "service_role";

revoke select on table "public"."pipit_cluster" from "service_role";

revoke trigger on table "public"."pipit_cluster" from "service_role";

revoke truncate on table "public"."pipit_cluster" from "service_role";

revoke update on table "public"."pipit_cluster" from "service_role";

alter table "public"."pipit_cluster" drop constraint "pipit_cluster_room_id_fkey";

alter table "public"."pipit_cluster" drop constraint "pipit_cluster_pkey";

drop index if exists "public"."pipit_cluster_pkey";

drop table "public"."pipit_cluster";

create table "public"."ai_interpreters" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "room_name" text not null,
    "source_language" text,
    "target_language" text,
    "status" ai_interpreter_status,
    "server_id" bigint
);


alter table "public"."ai_interpreters" enable row level security;

create table "public"."servers" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "ssh_hostname" text not null,
    "ssh_port" smallint not null,
    "runpod_id" text,
    "status" pipit_status not null default 'UNASSIGNED'::pipit_status,
    "last_used_at" timestamp without time zone default now()
);


alter table "public"."servers" enable row level security;

CREATE UNIQUE INDEX ai_interpreters_pkey ON public.ai_interpreters USING btree (id);

CREATE UNIQUE INDEX pipit_cluster_pkey ON public.servers USING btree (id);

alter table "public"."ai_interpreters" add constraint "ai_interpreters_pkey" PRIMARY KEY using index "ai_interpreters_pkey";

alter table "public"."servers" add constraint "pipit_cluster_pkey" PRIMARY KEY using index "pipit_cluster_pkey";

alter table "public"."ai_interpreters" add constraint "ai_interpreters_room_name_fkey" FOREIGN KEY (room_name) REFERENCES room_info(room_name) not valid;

alter table "public"."ai_interpreters" validate constraint "ai_interpreters_room_name_fkey";

alter table "public"."ai_interpreters" add constraint "ai_interpreters_server_id_fkey" FOREIGN KEY (server_id) REFERENCES servers(id) not valid;

alter table "public"."ai_interpreters" validate constraint "ai_interpreters_server_id_fkey";

grant delete on table "public"."ai_interpreters" to "anon";

grant insert on table "public"."ai_interpreters" to "anon";

grant references on table "public"."ai_interpreters" to "anon";

grant select on table "public"."ai_interpreters" to "anon";

grant trigger on table "public"."ai_interpreters" to "anon";

grant truncate on table "public"."ai_interpreters" to "anon";

grant update on table "public"."ai_interpreters" to "anon";

grant delete on table "public"."ai_interpreters" to "authenticated";

grant insert on table "public"."ai_interpreters" to "authenticated";

grant references on table "public"."ai_interpreters" to "authenticated";

grant select on table "public"."ai_interpreters" to "authenticated";

grant trigger on table "public"."ai_interpreters" to "authenticated";

grant truncate on table "public"."ai_interpreters" to "authenticated";

grant update on table "public"."ai_interpreters" to "authenticated";

grant delete on table "public"."ai_interpreters" to "service_role";

grant insert on table "public"."ai_interpreters" to "service_role";

grant references on table "public"."ai_interpreters" to "service_role";

grant select on table "public"."ai_interpreters" to "service_role";

grant trigger on table "public"."ai_interpreters" to "service_role";

grant truncate on table "public"."ai_interpreters" to "service_role";

grant update on table "public"."ai_interpreters" to "service_role";

grant delete on table "public"."servers" to "anon";

grant insert on table "public"."servers" to "anon";

grant references on table "public"."servers" to "anon";

grant select on table "public"."servers" to "anon";

grant trigger on table "public"."servers" to "anon";

grant truncate on table "public"."servers" to "anon";

grant update on table "public"."servers" to "anon";

grant delete on table "public"."servers" to "authenticated";

grant insert on table "public"."servers" to "authenticated";

grant references on table "public"."servers" to "authenticated";

grant select on table "public"."servers" to "authenticated";

grant trigger on table "public"."servers" to "authenticated";

grant truncate on table "public"."servers" to "authenticated";

grant update on table "public"."servers" to "authenticated";

grant delete on table "public"."servers" to "service_role";

grant insert on table "public"."servers" to "service_role";

grant references on table "public"."servers" to "service_role";

grant select on table "public"."servers" to "service_role";

grant trigger on table "public"."servers" to "service_role";

grant truncate on table "public"."servers" to "service_role";

grant update on table "public"."servers" to "service_role";

create policy "Enable insert for authenticated users only"
on "public"."ai_interpreters"
as permissive
for insert
to authenticated
with check (true);


create policy "Enable insert for authenticated users only"
on "public"."servers"
as permissive
for insert
to authenticated
with check (true);



