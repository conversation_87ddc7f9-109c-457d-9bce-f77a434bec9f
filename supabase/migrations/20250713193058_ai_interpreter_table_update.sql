alter table "public"."ai_interpreters" drop constraint "ai_interpreters_pkey";

drop index if exists "public"."ai_interpreters_pkey";

alter type "public"."ai_interpreter_status" rename to "ai_interpreter_status__old_version_to_be_dropped";

create type "public"."ai_interpreter_status" as enum ('PENDING', 'SERVER_ASSIGNED', 'STARTING', 'RUNNING', 'STOPPED');

alter table "public"."ai_interpreters" alter column status type "public"."ai_interpreter_status" using status::text::"public"."ai_interpreter_status";

drop type "public"."ai_interpreter_status__old_version_to_be_dropped";

alter table "public"."ai_interpreters" drop column "id";

alter table "public"."ai_interpreters" add column "running_at" timestamp with time zone;

alter table "public"."ai_interpreters" add column "server_assigned_at" timestamp with time zone;

alter table "public"."ai_interpreters" add column "starting_at" timestamp with time zone;

alter table "public"."ai_interpreters" add column "stopped_at" timestamp with time zone;

alter table "public"."ai_interpreters" add column "stopping_at" timestamp with time zone;

alter table "public"."ai_interpreters" add column "tmux_session_id" text;

alter table "public"."ai_interpreters" add column "uuid" uuid not null default gen_random_uuid();

alter table "public"."ai_interpreters" alter column "target_language" set not null;

alter table "public"."servers" add column "capacity" smallint default '1'::smallint;

CREATE UNIQUE INDEX ai_interpreters_pkey ON public.ai_interpreters USING btree (uuid);

alter table "public"."ai_interpreters" add constraint "ai_interpreters_pkey" PRIMARY KEY using index "ai_interpreters_pkey";


