-- Enable Row Level Security on both tables
ALTER TABLE public.room_info ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tokens ENABLE ROW LEVEL SECURITY;

-- RLS POLICIES FOR room_info (server-side security layer with proper auth context)
-- Allow authenticated users to insert rooms (server provides creator_email)
CREATE POLICY "Authenticated users can insert rooms" ON public.room_info
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- All authenticated users can view all room_info entries
CREATE POLICY "Authenticated users can view all rooms" ON public.room_info
  FOR SELECT
  TO authenticated
  USING (true);

-- Allow authenticated users to update/delete (server will handle business logic)
CREATE POLICY "Authenticated users can update rooms" ON public.room_info
  FOR UPDATE
  TO authenticated
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Authenticated users can delete rooms" ON public.room_info
  FOR DELETE
  TO authenticated
  USING (true);

-- R<PERSON> POLICIES FOR tokens (server-side security layer)
-- All authenticated users can view all tokens
CREATE POLICY "Authenticated users can view all tokens" ON public.tokens
  FOR SELECT
  TO authenticated
  USING (true);

-- All authenticated users can insert tokens
CREATE POLICY "Authenticated users can insert tokens" ON public.tokens
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- All authenticated users can update tokens
CREATE POLICY "Authenticated users can update tokens" ON public.tokens
  FOR UPDATE
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- All authenticated users can delete tokens
CREATE POLICY "Authenticated users can delete tokens" ON public.tokens
  FOR DELETE
  TO authenticated
  USING (true);
