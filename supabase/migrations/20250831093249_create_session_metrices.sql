-- Session metrics by language pair
CREATE TABLE session_metrics (
    id BIGSERIAL PRIMARY KEY,
    session_id UUID NOT NULL,
    language_pair VARCHAR(50) NOT NULL,
    ai_interpreter_seconds NUMERIC(10,2),
    total_listeners_seconds NUMERIC(10,2),
    total_cost NUMERIC(10,4),
    listeners_count NUMERIC(8,1),
    total_ai_interpreter_cost NUMERIC(10,4),
    total_listeners_cost NUMERIC(10,4),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_session_id FOREIGN KEY (session_id) REFERENCES sessions(id),
    CONSTRAINT unique_session_language UNIQUE (session_id, language_pair)
);

-- Enable RLS on session_metrics table
ALTER TABLE session_metrics ENABLE ROW LEVEL SECURITY;

-- Create a function to check if user owns the session
CREATE OR REPLACE FUNCTION user_owns_session(session_uuid UUID)
RETURNS BOOLEAN AS $
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM sessions
        WHERE id = session_uuid AND user_id = auth.uid()
    );
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Policy to allow users to view only their own session metrics
CREATE POLICY "Users can view their own session metrics" ON session_metrics
    FOR SELECT USING (user_owns_session(session_id));
