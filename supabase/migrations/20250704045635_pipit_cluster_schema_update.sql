create type "public"."pipit_status" as enum ('UNASSIGNED', 'ASSIGNED', 'STARTING', 'RUNNING', 'STOPPING');

alter table "public"."pipit_cluster" drop column "is_busy";

alter table "public"."pipit_cluster" add column "status" pipit_status not null default 'UNASSIGNED'::pipit_status;

alter table "public"."pipit_cluster" alter column "last_used_at" set default now();

alter table "public"."pipit_cluster" enable row level security;

CREATE INDEX idx_tokens_assigned_cookie ON public.tokens USING btree (assigned_cookie);

CREATE INDEX idx_tokens_expires_at ON public.tokens USING btree (expires_at);

CREATE INDEX idx_tokens_last_used ON public.tokens USING btree (last_used_time);

CREATE INDEX idx_tokens_room_name ON public.tokens USING btree (room_name);

create policy "Enable insert for authenticated users only"
on "public"."pipit_cluster"
as permissive
for insert
to authenticated
with check (true);



