alter type "public"."ai_interpreter_status" rename to "ai_interpreter_status__old_version_to_be_dropped";

create type "public"."ai_interpreter_status" as enum ('PENDING', 'SERVER_ASSIGNED', 'STARTING', 'RUNNING', 'STOPPED', 'FAILED');

alter table "public"."ai_interpreters" alter column status type "public"."ai_interpreter_status" using status::text::"public"."ai_interpreter_status";

drop type "public"."ai_interpreter_status__old_version_to_be_dropped";

alter table "public"."room_info" add column "status" text;


  create policy "Enable delete for users based on user_id"
  on "public"."ai_interpreters"
  as permissive
  for delete
  to authenticated
using ((( SELECT auth.uid() AS uid) = user_id));



  create policy "Enable insert for users based on user_id"
  on "public"."ai_interpreters"
  as permissive
  for insert
  to authenticated
with check ((( SELECT auth.uid() AS uid) = user_id));



  create policy "Enable update for users based on user_id"
  on "public"."ai_interpreters"
  as permissive
  for update
  to public
using ((auth.uid() = user_id))
with check ((auth.uid() = user_id));



  create policy "Enable users to view their own data only"
  on "public"."ai_interpreters"
  as permissive
  for select
  to authenticated
using ((( SELECT auth.uid() AS uid) = user_id));



  create policy "Enable delete for users based on user_id"
  on "public"."servers"
  as permissive
  for delete
  to public
using ((( SELECT auth.uid() AS uid) = user_id));



  create policy "Enable insert for users based on user_id"
  on "public"."servers"
  as permissive
  for insert
  to public
with check ((( SELECT auth.uid() AS uid) = user_id));



  create policy "Enable users to view their own data only"
  on "public"."servers"
  as permissive
  for select
  to authenticated
using ((( SELECT auth.uid() AS uid) = user_id));



  create policy "Enable insert for authenticated users only"
  on "public"."sessions"
  as permissive
  for insert
  to authenticated
with check (true);



  create policy "Enable insert for users based on user_id"
  on "public"."sessions"
  as permissive
  for insert
  to public
with check ((( SELECT auth.uid() AS uid) = user_id));



  create policy "Enable read access for all users"
  on "public"."sessions"
  as permissive
  for select
  to public
using (true);



  create policy "Enable users to view their own data only"
  on "public"."sessions"
  as permissive
  for select
  to authenticated
using ((( SELECT auth.uid() AS uid) = user_id));



