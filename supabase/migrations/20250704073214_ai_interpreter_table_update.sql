alter type "public"."ai_interpreter_status" rename to "ai_interpreter_status__old_version_to_be_dropped";

create type "public"."ai_interpreter_status" as enum ('CREATED', 'SERVER_ASSIGNED', 'STARTING', 'RUNNING', 'STOPPED');

alter table "public"."ai_interpreters" alter column status type "public"."ai_interpreter_status" using status::text::"public"."ai_interpreter_status";

drop type "public"."ai_interpreter_status__old_version_to_be_dropped";

alter table "public"."servers" drop column "status";


