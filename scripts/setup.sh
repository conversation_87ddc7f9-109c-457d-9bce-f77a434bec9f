#!/bin/bash

# Remote Machine Setup Script with <PERSON><PERSON><PERSON> Handling
# Run this script directly on the target machine to install prerequisites and tools

set -euo pipefail  # Exit on error, undefined variables, and pipe failures

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Helper functions for formatted output
print_status() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Error handler function
error_handler() {
    local exit_code=$?
    local line_number=$1

    print_error "Setup failed at line $line_number with exit code $exit_code"
    print_error "Please check the logs above for more details"

    exit $exit_code
}

# Set up trap to catch errors
trap 'error_handler $LINENO' ERR

print_status "Starting machine setup with error handling..."

# Check if running as root or with sudo access
print_status "Checking user privileges..."
if [ "$EUID" -eq 0 ]; then
    SUDO_CMD=""
    print_status "Running as root, sudo not needed"
else
    SUDO_CMD="sudo"
    print_status "Running as non-root user, using sudo"
    # Test sudo access
    if ! sudo -n true 2>/dev/null; then
        print_status "Testing sudo access (you may be prompted for password)..."
        if ! sudo true; then
            print_error "This script requires sudo access to install packages"
            exit 1
        fi
    fi
fi

# Step 1: Update package list
print_status "Updating package list..."
$SUDO_CMD apt-get update
print_success "Package list updated"

# Step 2: Install prerequisite packages
print_status "Installing prerequisite tools and libraries..."
$SUDO_CMD apt-get install -y \
    tmux \
    git \
    curl \
    portaudio19-dev \
    libportaudio2 \
    build-essential
print_success "Prerequisite packages installed"

# Step 3: Install uv if not already installed
print_status "Checking for uv installation..."
if ! command -v uv &> /dev/null; then
    print_status "Installing uv..."
    curl -LsSf https://astral.sh/uv/install.sh | sh
    # Add uv to PATH for current session
    export PATH="$HOME/.local/bin:$PATH"
    print_success "uv installed successfully"
else
    print_success "uv already installed"
fi

# Step 4: Install gh CLI
print_status "Installing gh CLI..."
# Download gh CLI package
print_status "Downloading gh CLI from GitHub release..."
curl -fsSL -o /tmp/gh.deb https://github.com/cli/cli/releases/download/v2.74.0/gh_2.74.0_linux_amd64.deb

# Install gh CLI package
print_status "Installing gh CLI package..."
$SUDO_CMD dpkg -i /tmp/gh.deb

# Clean up downloaded file
rm -f /tmp/gh.deb
print_success "gh CLI installed successfully"

# Step 4.1: Login to Hugging Face CLI if token is available
print_status "Checking for Hugging Face token..."
if [ -n "${HF_TOKEN:-}" ]; then
    print_status "Logging in to Hugging Face CLI with provided token..."
    $HOME/.local/bin/uvx --from huggingface_hub huggingface-cli login --token "$HF_TOKEN"
    print_success "Successfully logged in to Hugging Face CLI"
else
    print_status "No HF_TOKEN environment variable found, skipping Hugging Face login"
    print_status "You can set HF_TOKEN environment variable and re-run this script to login"
fi

# Step 5: Download FastText language identification model
print_status "Creating models directory and downloading FastText model..."
$SUDO_CMD mkdir -p /root/models/fasttext

print_status "Downloading FastText language identification model..."
$SUDO_CMD wget -O /root/models/fasttext/lid.176.bin https://huggingface.co/julien-c/fasttext-language-id/resolve/0266da4549434de56667387618bc67dc6d2670ef/lid.176.bin

print_success "FastText language identification model downloaded successfully"
print_status "Model location: /root/models/fasttext/lid.176.bin"

# Step 6: Verify installations
print_status "Verifying installations..."

# Check git
if command -v git &> /dev/null; then
    print_success "git: $(git --version)"
else
    print_error "git installation verification failed"
    exit 1
fi

# Check curl
if command -v curl &> /dev/null; then
    print_success "curl: $(curl --version | head -n1)"
else
    print_error "curl installation verification failed"
    exit 1
fi

# Check uv
if command -v uv &> /dev/null || command -v "$HOME/.local/bin/uv" &> /dev/null; then
    # Try both locations for uv
    if command -v uv &> /dev/null; then
        print_success "uv: $(uv --version)"
    else
        print_success "uv: $($HOME/.local/bin/uv --version)"
    fi
else
    print_error "uv installation verification failed"
    exit 1
fi

# Check gh
if command -v gh &> /dev/null; then
    print_success "gh: $(gh --version | head -n1)"
else
    print_error "gh installation verification failed"
    exit 1
fi

# Check FastText model
print_status "Verifying FastText model integrity..."
if [ -f "/root/models/fasttext/lid.176.bin" ]; then
    # Calculate SHA256 hash of the downloaded file
    DOWNLOADED_HASH=$(sha256sum /root/models/fasttext/lid.176.bin | cut -d' ' -f1)
    EXPECTED_HASH="7e69ec5451bc261cc7844e49e4792a85d7f09c06789ec800fc4a44aec362764e"

    if [ "$DOWNLOADED_HASH" = "$EXPECTED_HASH" ]; then
        print_success "FastText model: File integrity verified (SHA256 hash matches)"
    else
        print_error "FastText model: File integrity check failed"
        print_error "Expected: $EXPECTED_HASH"
        print_error "Got:      $DOWNLOADED_HASH"
        exit 1
    fi
else
    print_error "FastText model: File not found at /root/models/fasttext/lid.176.bin"
    exit 1
fi

# Step 7: Clone repositories
print_status "Cloning repositories..."
gh repo clone VoiceFrom/pipit -- --depth 1 --single-branch
gh repo clone VoiceFrom/myna -- --depth 1 --single-branch
gh repo clone VoiceFrom/trogon -- --depth 1 --single-branch
print_success "Repositories cloned successfully"

print_success "Machine setup completed successfully!"
print_status ""
print_status "The following tools have been installed:"
print_status "  ✓ git - Version control system"
print_status "  ✓ curl - Data transfer tool"
print_status "  ✓ portaudio19-dev - Audio development libraries"
print_status "  ✓ libportaudio2 - Audio runtime libraries"
print_status "  ✓ build-essential - Compilation tools"
print_status "  ✓ uv - Python package manager"
print_status "  ✓ gh - GitHub CLI tool"
print_status ""
print_status "Note: If uv was just installed, you may need to reload your shell"
print_status "or run 'source ~/.bashrc' to use uv in new terminal sessions."
