# XXX: Probably should go somewhere else?
export PATH="$HOME/.local/bin:$PATH"
export CUDA_HOME='/usr/local/cuda'
export LD_LIBRARY_PATH="$CUDA_HOME/lib64:/usr/local/lib/python3.11/dist-packages/cusparselt/lib/${LD_LIBRARY_PATH:+:$LD_LIBRARY_PATH}"

room_name="$1"
source_lang="$2"
target_lang="$3"

if [ -z "$room_name" ] || [ -z "$target_lang" ] || [ -z "$source_lang" ]; then
    echo "Usage: run_bot.sh <room_name> <source_lang> <target_language>"
    exit 1
fi

cd pipit

# Validate target language exists
config_file="config/target_languages/${target_lang}.gin"
if [ ! -f "$config_file" ]; then
    echo "Error: Target language '$target_lang' not found."
    echo "Available target languages:"
    ls config/target_languages/ | sed 's/\.gin$//' | sed 's/^/  /'
    exit 1
fi

ARGS=(
    "run-bot"
    "--gin_file=config/bot.gin"
    "--gin_param='livekit_room=\"${room_name}\"'"
    "--gin_param=source_lang=%${source_lang}"
    "--gin_file=config/target_languages/${target_lang}.gin"
)

# Create a new tmux session with a descriptive name
SESSION=$(openssl rand -hex 16)
tmux new-session -d -s "$SESSION" \
    bash -c "
    echo 'Starting myna bot...'
    echo 'Room: ${room_name}'
    echo 'Source Language: ${source_lang}'
    echo 'Target Language: ${target_lang}'
    echo '================================'

    uv run ${ARGS[*]}

    echo 'Session kept alive for inspection.'
    echo 'Type \"exit\" to close this session.'
    exec bash"

echo "$SESSION"
