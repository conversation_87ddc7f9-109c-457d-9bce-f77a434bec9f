# https://openmeter.io/docs/collectors/how-it-works#architecture

# Input: Accept HTTP events from Python app
input:
  http_server:
    address: 0.0.0.0:8080
    path: /events
    allowed_verbs:
      - POST

# Pipeline: Event enrichment with pricing logic
# https://docs.redpanda.com/redpanda-connect/guides/bloblang/about/
# Debugging: https://docs.redpanda.com/redpanda-connect/components/processors/log/
pipeline:
  processors:
    - log:
        level: INFO
        message: "Processing message: ${! json() }"

    - mapping: |
        # Set the new document to have the old doc data
        root = this

        # Centralized pricing logic
        let ai_rate = 1.0 / 60.0      # $1.00 per minute
        let listener_rate = 0.1 / 60.0 # $0.10 per minute per listener

        let ai_cost = this.data.ai_duration_seconds * $ai_rate

        let listener_cost = this.data.listener_duration_seconds * $listener_rate

        # Total cost calculation
        let total_cost = $ai_cost + $listener_cost

        # Add calculated fields to event
        root.data.total_cost = $total_cost
        root.data.ai_cost = $ai_cost
        root.data.listener_cost = $listener_cost
        # Was designed for event batching where multiple listeners would be registered in one event

    - log:
        level: INFO
        message: "After processing: ${! json() }"

# Output: Send enriched events to OpenMeter
output:
  openmeter:
    url: https://openmeter.cloud
    token: "${OM_COLLECTOR_API_TOKEN}"
