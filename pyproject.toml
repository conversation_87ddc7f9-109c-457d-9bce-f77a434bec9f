[project]
name = "tiong"
version = "0.1.0"
description = "Backend & frontend code for simultaneous translation of live streaming events."
readme = "README.md"
authors = [
  { name = "<PERSON>", email = "<EMAIL>" },
  { name = "<PERSON><PERSON>", email = "<EMAIL>" },
]
requires-python = ">=3.11"
dependencies = [
  "aiosqlite",
  "asyncpg",
  "fastapi",
  "fire",
  "livekit-api",
  "loguru",
  "python-dotenv",
  "uvicorn[standard]",
  "fire",
  "asyncssh>=2.21.0",
  "pyjwt>=2.8.0",
  "httpx>=0.24.0",
  "cloudevents",
  "supabase>=2.18.1",
  "stripe",
]

[project.optional-dependencies]
test = ["pytest-asyncio"]

[project.scripts]
tiong = "tiong.main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.ruff]
lint.select = ["E", "F", "I"]
