# Stage 1: Get the OpenMeter collector binary from official image
FROM ghcr.io/openmeterio/benthos-collector AS collector

# Stage 2: Build main application
# Use Python 3.11 base image
FROM python:3.11-slim

RUN apt-get update && apt-get install -y \
  curl \
  supervisor \
  && curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
  && apt-get install -y nodejs \
  && rm -rf /var/lib/apt/lists/*

# Install uv
RUN pip install uv

# Set working directory
WORKDIR /app

# Copy Python dependency files and source code (needed for editable install)
COPY pyproject.toml uv.lock ./
COPY README.md ./
COPY src ./src

# Install Python dependencies
RUN uv sync --frozen

# Copy entire frontend directory
COPY frontend ./frontend

# Copy .env file to make environment variables available for frontend build
COPY dot-env-frontend ./.env

# Copy ssh keys
COPY --chmod=600 secrets/pipit_servers /app/pipit_servers

# Install frontend dependencies and build
RUN cd frontend && npm install && npm run build

# Copy the OpenMeter collector binary from stage 1
COPY --from=collector /usr/local/bin/benthos /usr/local/bin/openmeter-collector

# Copy collector configuration
COPY openmeter_collector/collector-config.yaml /etc/openmeter/config.yaml

# Copy runpod setup scripts & runpodctl binaries.
COPY scripts ./scripts
COPY tools ./tools
COPY secrets ./secrets

# Copy supervisord configuration
COPY supervisord.conf /etc/supervisor/supervisord.conf

# Expose FastAPI port
EXPOSE 8000

# Start supervisor (which starts both processes)
CMD ["supervisord", "-c", "/etc/supervisor/supervisord.conf"]
