# Tiong

## Developer Setup

### Prerequisites

- Install [Docker](https://docs.docker.com/engine/install/) for supabase
- Install [Supabase](https://supabase.com/docs/guides/local-development/cli/getting-started)
- Install [Fly CLI](https://fly.io/docs/hands-on/install-flyctl/)
- Install uv with `curl -LsSf https://astral.sh/uv/install.sh | sh`
- Install [Node.js](https://nodejs.org/) (version 18 or higher)
- Install [shdotenv](https://github.com/ko1nksm/shdotenv)

### First-time Setup

After cloning the repository, run these commands to set up your development environment:

1. **Install root dependencies and set up git hooks:**

   ```bash
   npm install
   ```

   This will automatically install <PERSON><PERSON> and set up pre-commit hooks.

2. **Install frontend dependencies:**

   ```bash
   cd frontend
   npm install
   cd ..
   ```

### Git Hooks

This project uses [<PERSON><PERSON>](https://typicode.github.io/husky/) to run pre-commit hooks that ensure code quality:

- **Prettier formatting** - Automatically formats frontend code
- **TypeScript type checking** - Validates TypeScript types

These hooks run automatically when you commit. If they fail, your commit will be rejected.

**Troubleshooting hooks:**

- If hooks don't run, ensure you ran `npm install` in the root directory
- If formatting fails, the pre-commit hook will show which files need attention
- If type checking fails, fix the TypeScript errors before committing

**Manual formatting (if needed):**

```bash
cd frontend
npm run format      # Fix formatting
npm run type-check  # Check types
```

## Local development

### Environment Setup

Copy and configure environment variables:

### Supabase

Start supabase

```bash
npm run supabase:start
```

To reset db
```bash
npm run supabase:db_reset
```

### Run prod version locally (requires openmeter collector)

```bash
npm run tiong:local
```

Server runs on http://localhost:8000

### OpenMeter Collector

[OpenMeter](https://openmeter.io/docs) is used for usage metering of the models. To get it working you need to set up the collector which applies event enrichment to calculate the total cost of an event before sending it to openmeter cloud.

```bash
cd openmeter_collector
docker compose up -d
```

### Stripe Webhook Testing

For testing Stripe webhooks locally, you need the Stripe CLI to forward webhook events to your local server:

1. **Start Stripe webhook listener:**

   ```bash
   stripe listen --events payment_intent.succeeded,payment_intent.payment_failed --forward-to localhost:8000/api/webhooks/stripe
   ```

2. **Test webhook with triggers:**

   ```bash
   # Test successful payment
   stripe trigger payment_intent.succeeded --override payment_intent:customer=cus_SuLQUonDeuM5eS

   # Test failed payment
   stripe trigger payment_intent.payment_failed
   ```

   Note: Replace `cus_SuLQUonDeuM5eS` with any valid OpenMeter customer key from your database.

### Run development with auto build

Terminal 1 - frontend:
```bash
cd frontend
npm install
npm run build:watch
```

Terminal 2 - backend:
```bash
uv run tiong
```

Development server runs on http://localhost:8000

## Run with Docker

The closest to Fly deployment, uses supervisor to run tiong and the openmeter collector together in one instance.

Step 1: Build

```bash
docker build -t tiong-local .
```

Step 2: Run

```bash
docker run --env-file .env --env-file openmeter_collector/.env -p 8000:8000 -p 8080:8080 tiong-local
```

## Deploy to Fly.io

### First deployment (needed only for the first time when we deploy the app. Here just for reference).

**Step 0: create config files (should be run if config/fly.template.toml changed)**

```bash
bash config/generate_configs.sh
```

**Step 1: Initialize app (creates app on Fly.io)**

production

```bash
flyctl launch --no-deploy -c config/production/fly.toml
```

staging

```bash
flyctl launch --no-deploy -c config/staging/fly.toml
```

**Step 2: Set environment variables**

```bash
flyctl secrets import < .env
```

**Step 3: Verify secrets (optional)**

```bash
flyctl secrets list
```

### Deployment

**Step 4: Deploy the app to staging**

```bash
flyctl deploy -c config/staging/fly.toml
```

**Step 5: Deploy the app on production**

```bash
flyctl deploy -c config/production/fly.toml
```

**Step 6: Access your app**

```bash
flyctl open -a tiong-app
```
